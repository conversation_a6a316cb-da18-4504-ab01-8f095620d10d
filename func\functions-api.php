<?php
defined( 'ABSPATH' ) || die();

define( 'AJAX_URL', admin_url( 'admin-ajax.php' ) );

define( 'API_URL', 'http://92.42.164.86:3232/polymedica30/' );
define( 'API_LOGIN', 'polymedica86.com' );
define( 'API_PASSWORD', 'K@Eut8XDvF5b0f' );
// define( 'API_URL', 'http://176.214.167.44/test/' );
// define( 'API_LOGIN', 'Администратор' );
// define( 'API_PASSWORD', '378310' );

function get_auth_headers() {
	return array(
		'Authorization' => 'Basic ' . base64_encode( API_LOGIN . ':' . API_PASSWORD ),
	);
}


function register_doctor_taxonomies() {
	// Таксономия для должностей
	register_taxonomy(
		'position',
		'doctor',
		array(
			'labels'            => array(
				'name'          => 'Должности',
				'singular_name' => 'Должность',
			),
			'hierarchical'      => true,
			'show_admin_column' => true,
			'rewrite'           => array( 'slug' => 'position' ),
		)
	);

	// Таксономия для категорий должностей
	register_taxonomy(
		'position_category',
		'doctor',
		array(
			'labels'            => array(
				'name'          => 'Категории должностей',
				'singular_name' => 'Категория должности',
			),
			'hierarchical'      => true,
			'show_admin_column' => true,
			'rewrite'           => array( 'slug' => 'position-category' ),
		)
	);

	// Таксономия для подразделения
	register_taxonomy(
		'podrazdelenie',
		'doctor',
		array(
			'labels'            => array(
				'name'          => 'Подразделения',
				'singular_name' => 'Подразделение',
			),
			'hierarchical'      => true,
			'show_admin_column' => true,
			'rewrite'           => array( 'slug' => 'podrazdelenie' ),
		)
	);

	// Таксономия для расписания
	register_taxonomy(
		'schedule',
		'doctor',
		array(
			'labels'            => array(
				'name'          => 'Расписание',
				'singular_name' => 'Дата и время',
			),
			'hierarchical'      => false,
			'show_admin_column' => false,
			'rewrite'           => array( 'slug' => 'schedule' ),
		)
	);
}
add_action( 'init', 'register_doctor_taxonomies' );


function update_position( $position_name, $position_code ) {
	$existing_term = term_exists( $position_name, 'position' );
	if ( $existing_term && ! is_wp_error( $existing_term ) ) {
		wp_update_term(
			$existing_term['term_id'],
			'position',
			array(
				'slug'        => transliterate_to_slug( $position_name ),
				'description' => $position_code,
			)
		);
	} else {
		wp_insert_term(
			$position_name,
			'position',
			array(
				'slug'        => transliterate_to_slug( $position_name ),
				'description' => $position_code,
			)
		);
	}
}

function update_service( $service_name, $service_code ) {
	$existing_term = term_exists( $service_name, 'service' );
	if ( $existing_term && ! is_wp_error( $existing_term ) ) {
		wp_update_term(
			$existing_term['term_id'],
			'service',
			array(
				'slug'        => transliterate_to_slug( $service_name ),
				'description' => $service_code,
			)
		);
		return $existing_term['term_id'];
	} else {
		$result = wp_insert_term(
			$service_name,
			'service',
			array(
				'slug'        => transliterate_to_slug( $service_name ),
				'description' => $service_code,
			)
		);
		if ( ! is_wp_error( $result ) ) {
			return $result['term_id'];
		}
	}
	return null;
}

function get_positions( $term_id ) {
	$position_name = get_term_meta( $term_id, 'position_name', true );
	$position_code = get_term_meta( $term_id, 'position_code', true );
	return array(
		'name' => $position_name,
		'code' => $position_code,
	);
}

/**
 * Updates all positions from xml file.
 *
 * @return void
 */
function update_positions_from_xml() {
	$url = API_URL . 'hs/Medicina/Sotrudnic/%D0%94%D0%BE%D0%BB%D0%B6%D0%BD%D0%BE%D1%81%D1%82%D0%B8';

	$headers = get_auth_headers();

	$response = wp_remote_get( $url, array( 'headers' => $headers ) );

	if ( wp_remote_retrieve_response_code( $response ) == 200 ) {
		$xml_data = wp_remote_retrieve_body( $response );
		$xml      = simplexml_load_string( $xml_data );

		foreach ( $xml->children() as $child ) {
			$tag_name = $child->getName();
			$code     = (int) substr( $tag_name, strpos( $tag_name, '_' ) + 1 );
			$name     = (string) $child['Должность'];

			// echo  transliterate_to_slug($name);

			update_position( $name, $code );

			$positions_from_xml[] = $name;
		}

		// Удаление терминов, которые отсутствуют в обновленном XML
		$existing_terms = get_terms(
			array(
				'taxonomy'   => 'position',
				'hide_empty' => false,
			)
		);

		foreach ( $existing_terms as $term ) {
			if ( ! in_array( $term->name, $positions_from_xml ) ) {
				wp_delete_term( $term->term_id, 'position' );
			}
		}
	}
}


/**
 * Регистрация крона для апдейта докторов раз в сутки
 */
function schedule_update_all_doctors() {
	if ( ! wp_next_scheduled( 'update_all_doctors_event' ) ) {
		wp_schedule_event( time(), 'daily', 'update_all_doctors_event' );
	}
}
add_action( 'wp', 'schedule_update_all_doctors' );

// Удаление задачи при деактивации темы
function unschedule_update_all_doctors() {
	$timestamp = wp_next_scheduled( 'update_all_doctors_event' );
	if ( $timestamp ) {
		wp_unschedule_event( $timestamp, 'update_all_doctors_event' );
	}
}
register_deactivation_hook( __FILE__, 'unschedule_update_all_doctors' );


add_action( 'update_all_doctors_event', 'update_all_doctors_from_xml' );


function update_all_doctors_from_xml() {
	update_doctors_from_xml( '00-000001' );
	update_doctors_from_xml( '00-000002' );
}

function update_doctors_from_xml( $podrazdelenie ) {
	$url = API_URL . '/hs/Medicina/Sotrudnic/Врачи_КодПодразделения_' . $podrazdelenie;

	$headers = get_auth_headers();

	$response = wp_remote_get( $url, array( 'headers' => $headers ) );

	if ( wp_remote_retrieve_response_code( $response ) == 200 ) {
		$xml_data = wp_remote_retrieve_body( $response );
		$xml      = simplexml_load_string( $xml_data );

		foreach ( $xml->children() as $child ) {
			$tag_name = $child->getName();
			$code     = (int) substr( $tag_name, strpos( $tag_name, '_' ) + 1 );
			$name     = (string) $child['Сотрудник'];
			$fio      = (string) $child['ФИО'];

			update_doctors( $name, $code, $fio, $podrazdelenie );
		}
	}
}



add_action( 'wp_ajax_my_make_appoitment', 'ajax_make_appoitment' );
add_action( 'wp_ajax_nopriv_my_make_appoitment', 'ajax_make_appoitment' );

function ajax_make_appoitment() {
	// Эта функция отключена
	// функционал в page-sended.php

	$data = json_decode( stripslashes( $_POST['data'] ), true );

	$datetime = DateTime::createFromFormat( 'd.m.Y H:i', $data['date'] . ' ' . $data['start_time'] );

	$is_child = false;

	$birth_date = DateTime::createFromFormat( 'd-m-y', $data['birthDate'] );
	if ( $birth_date ) {
		$age_difference = ( new DateTime() )->diff( $birth_date );
		$is_child       = $age_difference->y < 16;
	}

	$formatted_date = $datetime->format( 'YmdHis' );

	$appointment_data = array(
		'КодПодразделения' => '00-000001',
		'Дата'             => $formatted_date,
		'КодСотрудника'    => $data['doctor_id'],
		'КодРабочееМесто'  => $data['work_place'],
		'КодНоменклатуры'  => $data['service_code'],
		'ФИО'              => $data['name'],
		'Телефон'          => $data['phone'],
		'Ребенок'          => $is_child,
	);

	$url = get_appointment_url( $appointment_data );

	echo $url;

	// $response = server_remote_get( $url );

	// if ( wp_remote_retrieve_response_code( $response ) == 200 ) {
	//  echo 'Запись успешно сделана';
	// } else {
	//  echo 'Запись не сделана';
	// }
}

function get_appointment_url( $data ) {
	$url  = API_URL . 'hs/Medicina/Sotrudnic/Записаться';
	$url .= '_КодПодразделения_' . $data['КодПодразделения'];
	$url .= '_Дата_' . $data['Дата'];
	$url .= '_КодСотрудника_' . $data['КодСотрудника'];
	$url .= '_КодРабочееМесто_' . $data['КодРабочееМесто'];
	$url .= '_КодНоменклатуры_' . $data['КодНоменклатуры'];
	$url .= '_ФИО_' . rawurlencode( $data['ФИО'] );
	$url .= '_Телефон_' . rawurlencode( $data['Телефон'] );
	$url .= $data['Ребенок'] ? '_Ребенок_' . $data['Ребенок'] : '';
	$url .= '_ДатаРождения_' . $data['ДатаРождения'];
	return $url;
}

function server_remote_get( $url ) {

	$headers = get_auth_headers();

	$response = wp_remote_get( $url, array( 'headers' => $headers ) );

	return $response;
}

function update_schedules_from_xml( $position_code, $search_date ) {
	$url = API_URL . "hs/Medicina/Sotrudnic/%D0%A3%D1%81%D0%BB%D1%83%D0%B3%D0%B8_%D0%9A%D0%BE%D0%B4%D0%9F%D0%BE%D0%B4%D1%80%D0%B0%D0%B7%D0%B4%D0%B5%D0%BB%D0%B5%D0%BD%D0%B8%D1%8F_00-000001_%D0%9A%D0%BE%D0%B4%D0%94%D0%BE%D0%BB%D0%B6%D0%BD%D0%BE%D1%81%D1%82%D1%8C_{$position_code}_%D0%94%D0%B0%D1%82%D0%B0_{$search_date}";

	log_to_file( 'API Request URL: ' . $url );

	$headers = get_auth_headers();

	$response = wp_remote_get( $url, array( 'headers' => $headers ) );

	if ( wp_remote_retrieve_response_code( $response ) == 200 ) {
		$xml_data = wp_remote_retrieve_body( $response );
		$dom      = new DOMDocument();
		$dom->loadXML( $xml_data );
		$root = $dom->documentElement;

		$schedules = array();

		foreach ( $root->childNodes as $schedule_tag ) {
			if ( $schedule_tag->nodeType === XML_ELEMENT_NODE ) {

				$schedule = array(
					'date'       => $schedule_tag->getAttribute( 'Дата' ),
					'start_time' => $schedule_tag->getAttribute( 'ВремяНачала' ),
					'end_time'   => $schedule_tag->getAttribute( 'ВремяОкончания' ),
				);

				foreach ( $schedule_tag->childNodes as $doctor_tag ) {
					if ( $doctor_tag->nodeType === XML_ELEMENT_NODE ) {
						$schedule['doctor_id'] = $doctor_tag->getAttribute( 'КодСотрудника_' );
						$schedule['fio']       = $doctor_tag->getAttribute( 'ФИО' );

						foreach ( $doctor_tag->childNodes as $service_tag ) {
							if ( $service_tag->nodeType === XML_ELEMENT_NODE ) {
								if ( $service_tag->nodeName === 'd4p1:КодРабочееМесто_' ) {
									$schedule['work_place'] = $service_tag->getAttribute( 'xmlns:d4p1' );
									continue;
								}
								$service['code']        = $service_tag->getAttribute( 'xmlns:d4p1' );
								$service['name']        = $service_tag->getAttribute( 'НаименованиеНоменклатуры_' );
								$service['time']        = $service_tag->getAttribute( 'ВремяВыполнения' );
								$service['price']       = $service_tag->getAttribute( 'Цена' );
								$service['is_first']    = 'истина' === $service_tag->getAttribute( 'первичный' ) ? true : false;
								$schedule['services'][] = $service;
							}
						}
					}
				}
				$schedules[] = $schedule;
			}
		}
		return $schedules;
	}
	return null;
}


function rebuild_schedules( $schedules ) {
	$result = array();
	foreach ( $schedules as $item ) {
		$doctor_id = $item['doctor_id'];
		if ( ! isset( $result[ $doctor_id ] ) ) {
			$result[ $doctor_id ] = array();
		}
		$result[ $doctor_id ][] = $item;
	}
	return $result;
}

function get_week_days( $schedules ) {
	$day       = array_values( $schedules )[0][0]['date'];
	$day_date  = strtotime( 'Monday', strtotime( $day ) );
	$week_days = array( 'ПН', 'ВТ', 'СР', 'ЧТ', 'ПТ', 'СБ', 'ВС' );
	$result    = array();
	foreach ( $week_days as $week_day ) {
		$result[ $week_day ] = $day_date;
		$day_date            = strtotime( '+1 day', $day_date );
	}
	return $result;
}

function get_schedules( $position_code, $date ) {
	$schedules = array();
	
	// Первый запрос по исходной дате
	$initial_schedules = update_schedules_from_xml( $position_code, $date );
	if ( $initial_schedules ) {
		$schedules = array_merge( $schedules, $initial_schedules );
	}

	// Запросы на следующие 3 недели от текущей даты
	for ( $week = 1; $week <= 3; $week++ ) {
		$week_date = date( 'Ymd', strtotime( "+{$week} week", strtotime( $date ) ) );
		log_to_file( 'Request date: ' . $week_date );
		
		$week_schedules = update_schedules_from_xml( $position_code, $week_date );
		
		if ( $week_schedules ) {
			$schedules = array_merge( $schedules, $week_schedules );
		}
	}
	
	if ( empty( $schedules ) ) {
		return null;
	}
	
	// перестраиваем структуру, делая ключем doctor_id
	$rebuilded_schedules = rebuild_schedules( $schedules );
	// обновляем специальности у врачей
	$doctor_ids = array_keys( $rebuilded_schedules );
	set_positions_to_doctors( $doctor_ids, $position_code );
	
	return $rebuilded_schedules;
}

function set_positions_to_doctors( $doctor_codes, $position_code ) {
	log_to_file('Начало обновления позиций для докторов', 'positions_update.log');
	log_to_file('Коды докторов: ' . implode(', ', $doctor_codes), 'positions_update.log');
	log_to_file('Код позиции: ' . $position_code, 'positions_update.log');
	
	$position = get_term_by_position_code( $position_code );
	$position_name = $position ? $position->name : 'Позиция не найдена';
	log_to_file('Название позиции: ' . $position_name, 'positions_update.log');
	
	foreach ( $doctor_codes as $doctor_code ) {
		$doctor = get_doctor_from_code( $doctor_code );
		
		if ( $doctor && $position ) {
			$doctor_fio = get_doctor_fio( $doctor->ID );
			log_to_file("Обновление позиции для доктора: Код={$doctor_code}, ФИО={$doctor_fio}, ID={$doctor->ID}, Позиция={$position_name} (ID={$position->term_id})", 'positions_update.log');
			update_doctor_positions( $doctor->ID, $position->term_id );
		} else {
			if (!$doctor) {
				log_to_file("Ошибка: Доктор с кодом {$doctor_code} не найден", 'positions_update.log');
			}
			if (!$position) {
				log_to_file("Ошибка: Позиция с кодом {$position_code} не найдена", 'positions_update.log');
			}
		}
	}
	
	log_to_file('Завершение обновления позиций для докторов', 'positions_update.log');
}


function update_doctor_positions( $doctor_id, $term_id ) {
	wp_set_post_terms( $doctor_id, array( $term_id ), 'position' );
}


function update_doctors( $name, $code, $fio, $podrazdelenie ) {
	$args = array(
		'post_type'      => 'doctor',
		'meta_key'       => 'code',
		'meta_value'     => $code,
		'posts_per_page' => 1,
		'post_status'    => array( 'publish', 'draft', 'pending' ),
	);

	// Проверка существует ли подразделение и получение его id
	$podrazdelenie_term = get_term_by( 'name', $podrazdelenie, 'podrazdelenie' );
	if ( ! $podrazdelenie_term ) {
		$podrazdelenie_term = wp_insert_term( $podrazdelenie, 'podrazdelenie' );
	}
	$podrazdelenie_id = $podrazdelenie_term->term_id;

	$posts = get_posts( $args );

	if ( $posts ) {
		// Апдейтнуть пост
		$post_id   = $posts[0]->ID;
		$post_data = array(
			'ID'         => $post_id,
			'post_title' => $name,
			//'post_status' => 'publish',
		);
		wp_update_post( $post_data );
		update_post_meta( $post_id, 'code', $code );
		update_post_meta( $post_id, 'fio', $fio );
		wp_set_object_terms( $post_id, $podrazdelenie_id, 'podrazdelenie' );
	} else {
		// Добавить новый пост
		$post_data = array(
			'post_title'  => $name,
			'post_type'   => 'doctor',
			'post_status' => 'publish',
		);
		$post_id   = wp_insert_post( $post_data );
		add_post_meta( $post_id, 'code', $code );
		add_post_meta( $post_id, 'fio', $fio );
		wp_set_object_terms( $post_id, $podrazdelenie_id, 'podrazdelenie' );
	}
}


function create_custom_tables() {
	global $wpdb;
	$charset_collate = $wpdb->get_charset_collate();

	// Таблица расписания
	$schedule_table     = $wpdb->prefix . 'schedule';
	$sql_schedule_table = "CREATE TABLE IF NOT EXISTS $schedule_table (
        id INT AUTO_INCREMENT PRIMARY KEY,
        doctor_id INT NOT NULL,
        date DATE NOT NULL,
        start_time TIME NOT NULL,
        end_time TIME NOT NULL,
        location VARCHAR(255) DEFAULT NULL
    ) $charset_collate;";

	// Таблица услуг
	$services_table     = $wpdb->prefix . 'services';
	$sql_services_table = "CREATE TABLE IF NOT EXISTS $services_table (
        id INT AUTO_INCREMENT PRIMARY KEY,
        code VARCHAR(20) NOT NULL UNIQUE,
        name VARCHAR(255) NOT NULL,
        execution_time TIME DEFAULT NULL
    ) $charset_collate;";

	// Таблица связей расписания и услуг
	$schedule_services_table     = $wpdb->prefix . 'schedule_services';
	$sql_schedule_services_table = "CREATE TABLE IF NOT EXISTS $schedule_services_table (
        id INT AUTO_INCREMENT PRIMARY KEY,
        schedule_id INT NOT NULL,
        service_id INT NOT NULL,
        FOREIGN KEY (schedule_id) REFERENCES $schedule_table(id) ON DELETE CASCADE,
        FOREIGN KEY (service_id) REFERENCES $services_table(id) ON DELETE CASCADE
    ) $charset_collate;";

	require_once ABSPATH . 'wp-admin/includes/upgrade.php';
	dbDelta( $sql_schedule_table );
	dbDelta( $sql_services_table );
	dbDelta( $sql_schedule_services_table );
}

register_activation_hook( __FILE__, 'create_custom_tables' );

function add_schedule_entry( $doctor_id, $date, $start_time, $end_time, $location = null ) {
	global $wpdb;
	$schedule_table = $wpdb->prefix . 'schedule';

	// Проверяем, есть ли уже запись с этим doctor_id, date и start_time
	$existing_schedule = $wpdb->get_row(
		$wpdb->prepare(
			"SELECT id FROM $schedule_table WHERE doctor_id = %d AND date = %s AND start_time = %s",
			$doctor_id,
			$date,
			$start_time
		)
	);

	if ( $existing_schedule ) {
		$wpdb->update(
			$schedule_table,
			array(
				'end_time' => $end_time,
				'location' => $location,
			),
			array( 'id' => $existing_schedule->id )
		);

		return $existing_schedule->id;
	} else {
		$wpdb->insert(
			$schedule_table,
			array(
				'doctor_id'  => $doctor_id,
				'date'       => $date,
				'start_time' => $start_time,
				'end_time'   => $end_time,
				'location'   => $location,
			)
		);

		return $wpdb->insert_id;
	}
}

function transliterate_to_slug( $text ) {
	$translit_table = array(
		'а' => 'a',
		'б' => 'b',
		'в' => 'v',
		'г' => 'g',
		'д' => 'd',
		'е' => 'e',
		'ё' => 'e',
		'ж' => 'zh',
		'з' => 'z',
		'и' => 'i',
		'й' => 'y',
		'к' => 'k',
		'л' => 'l',
		'м' => 'm',
		'н' => 'n',
		'о' => 'o',
		'п' => 'p',
		'р' => 'r',
		'с' => 's',
		'т' => 't',
		'у' => 'u',
		'ф' => 'f',
		'х' => 'kh',
		'ц' => 'ts',
		'ч' => 'ch',
		'ш' => 'sh',
		'щ' => 'shch',
		'ы' => 'y',
		'э' => 'e',
		'ю' => 'yu',
		'я' => 'ya',
		'ь' => '',
		'ъ' => '',
		' ' => '-',
	);
	$text           = mb_strtolower( $text );
	$text           = strtr( $text, $translit_table );
	return sanitize_title( $text );
}



// Таблица для логов запросов к апи
function create_request_log_table() {
	global $wpdb;

	$table_name      = $wpdb->prefix . 'remote_requests_log';
	$charset_collate = $wpdb->get_charset_collate();

	// Проверяем, есть ли уже такая таблица
	if ( $wpdb->get_var( "SHOW TABLES LIKE '$table_name'" ) != $table_name ) {
		$sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            request_date datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            code_subdivision varchar(50) NOT NULL,
            date varchar(50) NOT NULL,
            doctor_code varchar(50) NOT NULL,
            work_place_code varchar(50) NOT NULL,
            service_code varchar(50) NOT NULL,
            full_name varchar(255) NOT NULL,
            phone varchar(50) NOT NULL,
            is_child tinyint(1) NOT NULL,
            birth_date varchar(50) NOT NULL,
            response_code int NOT NULL,
            response_body longtext NOT NULL,
            result varchar(50) NOT NULL,
            PRIMARY KEY (id)
        ) $charset_collate;";

		require_once ABSPATH . 'wp-admin/includes/upgrade.php';
		dbDelta( $sql );
	}
}

// Вызываем функцию при загрузке темы (один раз)
add_action( 'after_setup_theme', 'create_request_log_table' );



// Логирование запросов к апи
function log_remote_request( $appointment_data, $response ) {
	global $wpdb;

	// Проверяем, является ли ответ ошибкой
	if ( is_wp_error( $response ) ) {
		$status_code   = 0;
		$response_body = $response->get_error_message();
		$result        = 'ошибка';
	} else {
		$status_code   = wp_remote_retrieve_response_code( $response );
		$full_response = wp_remote_retrieve_body( $response );

		// Попытаемся извлечь ключевую информацию
		$decoded_response = json_decode( $full_response, true );
		$response_body    = isset( $decoded_response['message'] ) ? $decoded_response['message'] : substr( $full_response, 0, 255 ); // Берем "message" или 255 символов

		$result = ( $status_code === 200 ) ? 'успех' : 'ошибка';

		// Используем регулярное выражение для извлечения атрибута Результат
		if ( preg_match( '/<Записаться Результат="([^"]+)"/', $full_response, $matches ) ) {
			$result = $matches[1]; // Берем значение атрибута Результат
		} else {
			$result = substr( $full_response, 0, 255 ); // Если нет атрибута, берем часть строки
		}
	}

	// Вставляем данные в таблицу
	$wpdb->insert(
		$wpdb->prefix . 'remote_requests_log',
		array(
			'request_date'     => current_time( 'mysql' ),
			'code_subdivision' => $appointment_data['КодПодразделения'],
			'date'             => $appointment_data['Дата'],
			'doctor_code'      => $appointment_data['КодСотрудника'],
			'work_place_code'  => $appointment_data['КодРабочееМесто'],
			'service_code'     => $appointment_data['КодНоменклатуры'],
			'full_name'        => $appointment_data['ФИО'],
			'phone'            => $appointment_data['Телефон'],
			'is_child'         => $appointment_data['Ребенок'],
			'birth_date'       => $appointment_data['ДатаРождения'],
			'response_code'    => $status_code,
			'response_body'    => $response_body, // Сокращенный ответ
			'result'           => $result,
		),
		array(
			'%s',
			'%s',
			'%s',
			'%s',
			'%s',
			'%s',
			'%s',
			'%s',
			'%d',
			'%s',
			'%d',
			'%s',
			'%s',
		)
	);
}

// Добавляем страницу логов в меню
function add_logs_admin_page() {
	add_menu_page(
		'Логи запросов',          // Заголовок страницы
		'Логи API',               // Название в меню
		'manage_options',         // Кто может видеть (админ)
		'api_logs',               // slug страницы
		'display_logs_page',      // Функция вывода
		'dashicons-list-view',    // Иконка в меню
		25                        // Позиция в меню
	);
}
add_action( 'admin_menu', 'add_logs_admin_page' );


function display_logs_page() {
	global $wpdb;

	$table_name = $wpdb->prefix . 'remote_requests_log';

	// Параметры пагинации
	$per_page     = 10; // Количество записей на странице
	$current_page = isset( $_GET['paged'] ) ? max( 1, intval( $_GET['paged'] ) ) : 1;
	$offset       = ( $current_page - 1 ) * $per_page;

	// Получаем логи
	$logs = $wpdb->get_results(
		$wpdb->prepare( "SELECT * FROM $table_name ORDER BY request_date DESC LIMIT %d OFFSET %d", $per_page, $offset ),
		ARRAY_A
	);

	// Получаем общее количество записей
	$total_items = $wpdb->get_var( "SELECT COUNT(*) FROM $table_name" );
	$total_pages = ceil( $total_items / $per_page );

	?>
	<div class="wrap">
		<h1 class="wp-heading-inline">Логи API запросов</h1>

		<table class="wp-list-table widefat fixed striped">
			<thead>
				<tr>
					<th width="15%">Дата запроса</th>
					<th>Код ответа сервера</th>
					<th>Результат</th>
					<th>Врач</th>
					<th>Время записи</th>
					<th>ФИО</th>
					<th>Телефон</th>
				</tr>
			</thead>
			<tbody>
				<?php if ( ! empty( $logs ) ) : ?>
					<?php foreach ( $logs as $log ) : ?>
						<?
							$doctor = get_doctor_from_code( $log['doctor_code'] );
							$position = get_first_doctor_position( $doctor->ID ) -> name;
							$date = DateTime::createFromFormat( 'YmdHis', $log['date'] );
							$date = $date->format('Y-m-d H:i:s');

							$status_style = $log['response_code'] == 200 ? 'green' : 'red' ;
							$result_style = $log['result'] === 'Успех' ? 'green' : 'red';
						?>
						<tr>
							<td><?php echo esc_html( $log['request_date'] ); ?></td>
							<td>
								<div style="color:<?php echo $status_style; ?>">
									<?php echo esc_html( $log['response_code'] ); ?>
								</div>
							</td>
							<td>
								<div style="color:<?php echo $result_style; ?>">
									<?php echo esc_html( $log['result'] ); ?>
								</div>
							</td>
							<td>
								<a href="<?php echo esc_url( get_link( $doctor ) ); ?>">
									<?php echo esc_html( get_doctor_fio( $doctor->ID ) ); ?>
								</a>
								(<?php echo esc_html( $position ); ?>)
							</td>
							<td><?php echo esc_html( $date ); ?></td>
							<td><?php echo esc_html( $log['full_name'] ); ?></td>
							<td><?php echo esc_html( $log['phone'] ); ?></td>
						</tr>
					<?php endforeach; ?>
				<?php else : ?>
					<tr><td colspan="6">Нет записей.</td></tr>
				<?php endif; ?>
			</tbody>
		</table>

		<!-- Пагинация -->
		<div class="tablenav">
			<div class="tablenav-pages">
				<?php
				$base = add_query_arg( 'paged', '%#%' );
				echo paginate_links(
					array(
						'base'      => $base,
						'format'    => '',
						'current'   => $current_page,
						'total'     => $total_pages,
						'prev_text' => __( '&laquo;' ),
						'next_text' => __( '&raquo;' ),
					)
				);
				?>
			</div>
		</div>
	</div>
	<?php
}


// Выводим список должностей
function echo_services() {
	update_positions_from_xml();

	$jobtitles = get_posts(
		array(
			'post_type'   => 'jobtitle',
			'numberposts' => -1,
		)
	);

	$categories = get_terms(
		array(
			'taxonomy'   => 'position_category',
			'hide_empty' => false,
		)
	);

	// Массив для хранения должностей по категориям
	$category_lists = array();
	// Массив для хранения всех должностей
	$showall_category = array();

	foreach ( $jobtitles as $position ) {
		$position_id       = $position->ID;
		$linked_categories = get_field( 'category_tabs', $position_id );

		$link = get_field( 'link', $position_id );

		if ( ! $link ) {
			$link = '/appointment/';
		}

		$list_item          = '<li><a href="' . esc_html( $link ) . '">' . esc_html( get_the_title( $position_id ) ) . '</a></li>';
		$showall_category[] = $list_item;

		if ( ! empty( $linked_categories ) ) {
			foreach ( $linked_categories as $category_id ) {
				// Добавляем должность в массив соответствующей категории
				$category_lists[ $category_id ][] = $list_item;
			}
		}
	}

	// Выводим все категории
	echo '<ul data-catid="0" class="category_ul">';
	echo implode( '', $showall_category );
	echo '</ul>';

	// Выводим категории и связанные с ними должности
	foreach ( $categories as $category ) {
		if ( empty( $category_lists[ $category->term_id ] ) ) {
			continue;
		}

		echo '<ul data-catid="' . esc_attr( $category->term_id ) . '" class="category_ul hide">';
		echo implode( '', $category_lists[ $category->term_id ] );
		echo '</ul>';
	}
}


// Выводим список должностей на главной
function echo_services_front() {
	update_positions_from_xml();

	$jobtitles = get_posts(
		array(
			'post_type'   => 'jobtitle',
			'numberposts' => -1,
		)
	);

	$categories = get_terms(
		array(
			'taxonomy'   => 'position_category',
			'hide_empty' => false,
		)
	);

	// Массив для хранения должностей по категориям
	$category_lists = array();
	// Массив для хранения всех должностей
	$showall_category = array();

	foreach ( $jobtitles as $position ) {
		$position_id       = $position->ID;
		$linked_categories = get_field( 'category_tabs', $position_id );

		$link = get_field( 'link', $position_id );

		if ( ! $link ) {
			$link = '/appointment/';
		}

		$list_item          = '<li><a href="' . esc_html( $link ) . '">' . esc_html( get_the_title( $position_id ) ) . '</a></li>';
		$showall_category[] = $list_item;

		if ( ! empty( $linked_categories ) ) {
			foreach ( $linked_categories as $category_id ) {
				// Добавляем должность в массив соответствующей категории
				$category_lists[ $category_id ][] = $list_item;
			}
		}
	}

	// Выводим все категории
	echo '<ul data-catid="0" class="category_ul">';
	echo implode( '', $showall_category );
	echo '</ul>';

	// Выводим категории и связанные с ними должности
	foreach ( $categories as $category ) {
		if ( empty( $category_lists[ $category->term_id ] ) ) {
			continue;
		}

		echo '<ul data-catid="' . esc_attr( $category->term_id ) . '" class="category_ul hide">';
		echo implode( '', $category_lists[ $category->term_id ] );
		echo '</ul>';
	}
}


function echo_var( $var ) {
	echo '<pre>';
	print_r( $var );
	echo '</pre>';
}

function consolelog( $var ) {
	echo '<script>';
	echo 'console.log(' . json_encode( $var ) . ')';
	echo '</script>';
}


function log_to_file( $content, $filename = 'output.txt' ) {
	$theme_directory = get_template_directory();

	$file_path = trailingslashit( $theme_directory ) . $filename;

	$content  = print_r( $content, true );
	$content .= PHP_EOL;

	$result = file_put_contents( $file_path, $content, FILE_APPEND );
}

/**
 * Получает список уникальных услуг доктора из API и сохраняет их
 *
 * @param string $doctor_code Код доктора
 * @return array|null Массив уникальных услуг или null в случае ошибки
 */
function get_doctor_servises_from_api( $doctor_code ) {
	$date = gmdate( 'Ymd' );

	$url = API_URL . 'hs/Medicina/Sotrudnic/Услуги_КодСотрудника_' . $doctor_code . '_Дата_' . $date;

	$headers = get_auth_headers();

	$response = wp_remote_get( $url, array( 'headers' => $headers ) );

	if ( wp_remote_retrieve_response_code( $response ) == 200 ) {
		$xml_data = wp_remote_retrieve_body( $response );
		$dom      = new DOMDocument();
		$dom->loadXML( $xml_data );
		$root = $dom->documentElement;

		// Проверяем, есть ли дочерние элементы
		if ( ! $root || ! $root->hasChildNodes() ) {
			return null;
		}

		$services = array();

		// Проходим по всем строкам расписания
		foreach ( $root->childNodes as $schedule_row ) {
			if ( $schedule_row->nodeType === XML_ELEMENT_NODE ) {
				// Проходим по всем исполнителям
				foreach ( $schedule_row->childNodes as $executor ) {
					if ( $executor->nodeType === XML_ELEMENT_NODE ) {
						// Проходим по всем услугам
						foreach ( $executor->childNodes as $service ) {
							if ( $service->nodeType === XML_ELEMENT_NODE && $service->nodeName !== 'd4p1:КодРабочееМесто_' ) {
								$service_code = $service->getAttribute( 'xmlns:d4p1' );

								// Если такой услуги еще нет в массиве
								if ( ! isset( $services[ $service_code ] ) ) {
									$services[ $service_code ] = array(
										'code'           => $service_code,
										'name'           => $service->getAttribute( 'НаименованиеНоменклатуры_' ),
										'execution_time' => $service->getAttribute( 'ВремяВыполнения' ),
										'is_primary'     => $service->getAttribute( 'первичный' ) === 'истина',
										'price'          => str_replace( ',', '.', $service->getAttribute( 'Цена' ) ),
									);
								}
							}
						}
					}
				}
			}
		}

		// Сохраняем услуги для доктора
		if ( ! empty( $services ) ) {
			$doctor = get_doctor_from_code( $doctor_code );
			if ( $doctor ) {
				$service_term_ids = array();

				// Создаем/обновляем термины услуг и собираем их ID
				foreach ( $services as $service ) {
					$term_id = update_service( $service['name'], $service['code'] );
					if ( $term_id ) {
						$service_term_ids[] = $term_id;

						// Сохраняем дополнительные данные услуги как мета-поля термина
						update_term_meta( $term_id, 'execution_time', $service['execution_time'] );
						update_term_meta( $term_id, 'is_primary', $service['is_primary'] );
						update_term_meta( $term_id, 'price', $service['price'] );
					}
				}

				// Привязываем услуги к врачу
				if ( ! empty( $service_term_ids ) ) {
					wp_set_post_terms( $doctor->ID, $service_term_ids, 'service' );
				}

				// Обновляем время последнего обновления
				update_post_meta( $doctor->ID, 'services_last_update', current_time( 'mysql' ) );

				return array_values( $services );
			}
		}
	}

	return null;
}

/**
 * Обновляет услуги всех докторов из API
 * Функция для запуска по крону
 */
function update_all_doctors_services() {
	$args = array(
		'post_type'      => 'doctor',
		'posts_per_page' => -1,
		'fields'         => 'ids',
		'post_status'    => 'publish',
	);

	$doctor_ids = get_posts( $args );

	foreach ( $doctor_ids as $doctor_id ) {
		$doctor_code = get_post_meta( $doctor_id, 'code', true );
		if ( $doctor_code ) {
			get_doctor_services( $doctor_code, true );
		}
	}
}

// Регистрируем крон-задачу для обновления 	
add_action( 'init', 'schedule_services_update' );
function schedule_services_update() {
	if ( ! wp_next_scheduled( 'update_doctors_services_event' ) ) {
		wp_schedule_event( time(), 'daily', 'update_doctors_services_event' );
	}
}

// Хук для выполнения обновления
add_action( 'update_doctors_services_event', 'update_all_doctors_services' );

// Очистка крон-задачи при деактивации
register_deactivation_hook( __FILE__, 'unschedule_services_update' );
function unschedule_services_update() {
	$timestamp = wp_next_scheduled( 'update_doctors_services_event' );
	if ( $timestamp ) {
		wp_unschedule_event( $timestamp, 'update_doctors_services_event' );
	}
}

/**
 * Миграция существующих данных services из метаполей в таксономию
 * Функция для одноразового запуска
 */
function migrate_services_to_taxonomy() {
	$args = array(
		'post_type'      => 'doctor',
		'posts_per_page' => -1,
		'fields'         => 'ids',
		'post_status'    => 'publish',
		'meta_query'     => array(
			array(
				'key'     => 'services',
				'compare' => 'EXISTS',
			),
		),
	);

	$doctor_ids = get_posts( $args );
	$migrated_count = 0;

	foreach ( $doctor_ids as $doctor_id ) {
		$services = get_post_meta( $doctor_id, 'services', true );

		if ( ! empty( $services ) && is_array( $services ) ) {
			$service_term_ids = array();

			foreach ( $services as $service ) {
				if ( isset( $service['name'] ) && isset( $service['code'] ) ) {
					$term_id = update_service( $service['name'], $service['code'] );
					if ( $term_id ) {
						$service_term_ids[] = $term_id;

						// Сохраняем дополнительные данные услуги как мета-поля термина
						if ( isset( $service['execution_time'] ) ) {
							update_term_meta( $term_id, 'execution_time', $service['execution_time'] );
						}
						if ( isset( $service['is_primary'] ) ) {
							update_term_meta( $term_id, 'is_primary', $service['is_primary'] );
						}
						if ( isset( $service['price'] ) ) {
							update_term_meta( $term_id, 'price', $service['price'] );
						}
					}
				}
			}

			// Привязываем услуги к врачу
			if ( ! empty( $service_term_ids ) ) {
				wp_set_post_terms( $doctor_id, $service_term_ids, 'service' );
				$migrated_count++;
			}
		}
	}

	return "Мигрировано услуг для {$migrated_count} врачей";
}

/**
 * Функция для тестирования миграции (можно вызвать через админку)
 * Добавить в functions.php: add_action('wp_ajax_test_migration', 'test_services_migration');
 */
function test_services_migration() {
	if ( current_user_can( 'manage_options' ) ) {
		$result = migrate_services_to_taxonomy();
		wp_die( $result );
	}
	wp_die( 'Недостаточно прав' );
}
