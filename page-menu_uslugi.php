<?php
/**
 * Template Name: Menu uslugi
 *
 * @package polidemika
 */

get_header();
?>
<style>
	.team { justify-content: start; }
	.team__card .card-info__position { overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
	
	.diw_h5 { font-weight: 500; font-size: 14px; line-height: 17.07px; letter-spacing: 2px; color: #AEB3C0; display: flex; justify-content: space-between; margin-bottom: 20px; margin-top: 20px; }
	.diw_oio_wrap { margin-bottom: 10px; }
	.diw_oio_row { margin-bottom: 20px; }
	.diw_oio_bubble { color: #fff; background: rgba(116, 172, 251, 1); padding: 5px 34px; margin-right: 20px; border-radius: 12px; }
	.diw_table { background: #fff; box-shadow: 0px 20px 30px 0px rgba(0, 0, 0, 0.05); padding: 16px 0px; margin-bottom: 80px; border-radius: 12px; display: flex; flex-wrap: wrap; justify-content: start; padding-left: 10%; }
	.diw_table a { padding: 10px 20px; color: #011424; text-decoration: none; width: 50%; }
	.diw_table a:hover { color: rgb(10 85 166); }

	.bigcat_name { margin-bottom: 40px; font-size: 32px; font-weight: 800; line-height: 39.01px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; }
	.post-thumbnail { margin-bottom: 40px; }
	.title_cat { color: rgba(174, 179, 192, 1); font-size: 16px; font-weight: 600; line-height: 19.5px; letter-spacing: 2px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; margin-bottom: 20px; }
	.entry-header { background: transparent; } 
	.entry-title { margin: 0 0 14px; font-size: 24px; font-weight: 800; line-height: 29.26px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; }
	.entry-content { margin-bottom: 60px; }
</style>
      
<?php
/**
 * Специалисты на главной
 */
?>

<div class="container mt20" id="specialists"> 
    <div class="block-f">
        <div class="block-title"><h1 class="entry-title"><?php the_title(); ?></h1></div>
<!--form>
  <div class="__select" data-state="">
    <div id="__select__title_id" class="__select__title" data-default="Выберите направление">Выберите направление</div>
    <div class="__select__content">
<?php
$categories = get_categories( [
	'taxonomy' => 'position',
	'orderby' => 'name',
	'order' => 'ASC'
] );
$i == 0;
foreach( $categories as $category ){ 
	$i++; ?>
	<input id="singleSelect<?php echo $i; ?>" class="__select__input" type="radio" name="singleSelect" />
	<label for="singleSelect<?php echo $i; ?>" class="__select__label" data-catid="<?php echo $category->term_id; ?>"><?php echo $category->name; ?></label>
<?php } ?>
    </div>
  </div>
</form-->		
    </div>
		

					<div class="diw_description" id="uslugi">
                        <div class="diw_table">
                        <?php
							
if( have_rows('usluga') ):
    while ( have_rows('usluga') ) : the_row();
        echo '<a href="' . get_sub_field('ssylka') . '">' . get_sub_field('nazvanie') . '</a>';
    endwhile;
endif;

                        ?>
                        </div>
                    </div>

	<?php 
	$doctors = get_field('vybor_doktorov');
	if ( $doctors ) : ?>
	<div class="team" style="">
		<?php foreach ( $doctors as $doctor ) : ?>
			<div class="team__card" data-term="<?php echo get_first_doctor_position( $doctor->ID )->term_id; ?>">
				<div class="card-foto">
					<img src="<?php echo get_doctor_photo( $doctor->ID ); ?>">
				</div>
				<div class="card-info"> 
					<div class="card-info__text">
					<div class="card-info__fio">
						<?php echo get_doctor_fio( $doctor->ID ); ?>
					</div>
					<div class="card-info__rating">
					<div class="stars"> </div>
					<div class="points"><?php echo ( get_field( 'score', $doctor->ID ) ) ? get_field( 'score', $doctor->ID ) : '0'; ?></div>
					</div>
					</div>
					<div class="card-info__position">
						<?php
							$position = get_first_doctor_position( $doctor->ID );
						if ( $position ) {
							echo $position->name;
						}
						?>
						
					</div>
					
				</div>
				<div class="card-buttons"> 
					<?php
						$hashItems = array();
						// Код направления
					if ( $doctor_position = term_description( $position ) ) {
						$doctor_position = strip_tags( $doctor_position );
						$hashItems[]     = "p:{$doctor_position}";
					}
						// Код врача
					if ( $doctor_code = get_post_meta( $doctor->ID, 'code', true ) ) {
						$doctor_code = str_pad( $doctor_code, 11, '0', STR_PAD_LEFT );
						$hashItems[] = "d:{$doctor_code}";
					}
					?>
					<a href="<?php echo home_url(); ?>/appointment#<?php echo implode( '_', $hashItems ); ?>">
						<button><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/appointment-blue.svg" alt="">Записаться на прием</button>
					</a>
					<a class="card-link" href="<?php echo $doctor->guid; ?>">Подробнее</a>
				</div>
			</div>
		
		<?php endforeach; ?>
		
	</div>
	<div class="entry-content">
			<?php the_content(); ?>
		</div>
	<?php endif;
	?>

	<div class="conts_form">
		<div class="cf_conts">
			<div class="cfc_wrap">
				<?php the_field('sami_kontakty', 887); ?>
				Наши соцсети<br>
				<div class="cf_soc_seti">
					<a href="<?php the_field('ssylka_vk', 887); ?>"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/vk_w.svg"></a>
					<a href="<?php the_field('ssylka_tg', 887); ?>"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/tg_w.svg"></a>
					<a href="<?php the_field('ssylka_yut', 887); ?>"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/yt_w.svg"></a>
				</div>
			</div>
			
		</div>
		
		<div class="cf_form">
			<?php echo do_shortcode('[contact-form-7 id="bf9b26f" title="Контактная форма 1"]'); ?>
		</div>
	</div>
	
	
</div>

					


    </div>
<?php
get_footer();