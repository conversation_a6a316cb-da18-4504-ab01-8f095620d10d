<?php
// Функции для работы с должностями (post-type - jobtitle)
defined( 'ABSPATH' ) || die();


// добавляем массовое редактирование в меню
function jobtitle_bulk_edit_menu() {
	add_submenu_page(
		'edit.php?post_type=jobtitle',
		'Массовое редактирование',
		'Редактировать все сразу',
		'manage_options',
		'edit-jobtitles',
		'jobtitle_bulk_edit_page'
	);
}
add_action( 'admin_menu', 'jobtitle_bulk_edit_menu' );

// Страница массового редактирования
function jobtitle_bulk_edit_page() {
	jobtitle_save_bulk();
	jobtitle_render_table();
}



// Сохранение данных из массового редактирования
function jobtitle_save_bulk() {
	if ( ! isset( $_POST['save_jobtitles'] ) ) {
		return;
	}

	foreach ( $_POST['jobtitles'] as $id => $title ) {

		if ( ! $title ) {
			continue;
		}

		$real_id = $id; // у новых записей пока нет ID

		if ( strpos( $id, 'new_' ) === 0 ) {
			$real_id = wp_insert_post(
				array(
					'post_title'  => sanitize_text_field( $title ),
					'post_type'   => 'jobtitle',
					'post_status' => 'publish',
				)
			);
			if ( ! $id ) {
				continue; // Пропустить, если не удалось создать запись
			}
		} else {
			wp_update_post(
				array(
					'ID'         => intval( $id ),
					'post_title' => sanitize_text_field( $title ),
				)
			);
		}

		// Сохраняем ACF поля
		if ( isset( $_POST['jobtitles_link'][ $id ] ) ) {
			update_field( 'link', sanitize_text_field( $_POST['jobtitles_link'][ $id ] ), $real_id );
		}

		if ( isset( $_POST['jobtitles_category_tabs'][ $id ] ) ) {
			update_field( 'category_tabs', $_POST['jobtitles_category_tabs'][ $id ], $real_id );
		}
	}

	echo '<div class="updated"><p>Сохранено!</p></div>';
}


// Вывод таблицы для массового редактирования
function jobtitle_render_table() {
	$jobtitles  = get_posts(
		array(
			'post_type'   => 'jobtitle',
			'numberposts' => -1,
		)
	);
	$categories = get_terms(
		array(
			'taxonomy'   => 'position_category',
			'hide_empty' => false,
		)
	);

	?>
	<form method="post" id="jobtitle-form">
		<table class="widefat">
			<thead>
				<tr>
					<th>ID</th>
					<th>Название</th>
					<th>Ссылка</th>
					<th>Категории</th>
				</tr>
			</thead>
			<tbody id="jobtitle-rows">
				<?php
				foreach ( $jobtitles as $jobtitle ) :
					$link          = get_field( 'link', $jobtitle->ID );
					$category_tabs = get_field( 'category_tabs', $jobtitle->ID );
					?>
					<tr>
						<td style="width:30px;"><?php echo esc_html( $jobtitle->ID ); ?></td>
						<td><input type="text" name="jobtitles[<?php echo esc_attr( $jobtitle->ID ); ?>]" value="<?php echo esc_attr( $jobtitle->post_title ); ?>" style="width:90%;"></td>
						<td><input type="text" name="jobtitles_link[<?php echo esc_attr( $jobtitle->ID ); ?>]" value="<?php echo esc_attr( $link ); ?>" style="width:90%;"></td>
						<td style="column-count: 3;">
							<?php
							foreach ( $categories as $category ) :
								$checked = ( is_array( $category_tabs ) && in_array( $category->term_id, $category_tabs ) ) ? 'checked' : '';
								?>
								<label><input type="checkbox" name="jobtitles_category_tabs[<?php echo esc_attr( $jobtitle->ID ); ?>][]" value="<?php echo esc_attr( $category->term_id ); ?>" <?php echo $checked; ?>> <?php echo esc_html( $category->name ); ?></label><br>
							<?php endforeach; ?>
						</td>
					</tr>
				<?php endforeach; ?>
			</tbody>
		</table>
		<button type="button" id="add-new-jobtitle" class="button" style="margin-top:20px">Добавить новую должность</button>
		<?php submit_button( 'Сохранить', 'primary', 'save_jobtitles' ); ?>
	</form>

	<script>
	document.getElementById("add-new-jobtitle").addEventListener("click", function() {
		let tableBody = document.getElementById("jobtitle-rows");
		let rowCount = tableBody.getElementsByTagName("tr").length;
		let newId = "new_" + rowCount;

		let newRow = document.createElement("tr");
		newRow.innerHTML = `
			<td>-</td>
			<td><input type="text" name="jobtitles[${newId}]" placeholder="Новая должность" style="width:90%;"></td>
			<td><input type="text" name="jobtitles_link[${newId}]" placeholder="Ссылка" style="width:90%;"></td>
			<td style="column-count: 3;">
				<?php foreach ( $categories as $category ) : ?>
					<label><input type="checkbox" name="jobtitles_category_tabs[${newId}][]" value="<?php echo esc_attr( $category->term_id ); ?>"> <?php echo esc_html( $category->name ); ?></label><br>
				<?php endforeach; ?>
			</td>
		`;
		tableBody.appendChild(newRow);
	});
	</script>
	<?php
}


// При создании новой таксономии должности(position) создавать новую запись должности(jobtitle)
function create_jobtitle_from_taxonomy( $term_id, $tt_id, $taxonomy ) {
	if ( $taxonomy === 'position' ) {
		$term = get_term( $term_id, $taxonomy );
		if ( $term && ! is_wp_error( $term ) ) {
			wp_insert_post(
				array(
					'post_title'  => $term->name,
					'post_type'   => 'jobtitle',
					'post_status' => 'publish',
				)
			);
		}
	}
}
add_action( 'created_position', 'create_jobtitle_from_taxonomy', 10, 3 );
