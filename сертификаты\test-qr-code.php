<?php
// test-qr-code.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// ==== НАСТРОЙКИ ====
$privateKeyPath = __DIR__ . '/certs/LB0000011859-ips-sysuser-staging-client.key';
$issuer = "https://c2b.sngb.ru/";
$subject = "ac-*********";
$audience = "https://c2b-cert.sngb.ru/am/ipslegals/connect/token";
$clientId = "pos-terminal";

$merchantId = "MB0000021993";           
$account = "40702810700000107522";   

// ==== ГЕНЕРАЦИЯ JWT ====
$now = time();
$exp = $now + 300;
$claims = [
    "iss" => $issuer,
    "sub" => $subject,
    "aud" => $audience,
    "iat" => $now,
    "exp" => $exp,
    "scope" => "read",
    "jti" => bin2hex(random_bytes(16))
];

$header = [
    "alg" => "RS256",
    "typ" => "JWT"
];

function base64url_encode($data) {
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

$jwtHeader = base64url_encode(json_encode($header));
$jwtPayload = base64url_encode(json_encode($claims));
$unsignedToken = "$jwtHeader.$jwtPayload";

$privateKey = openssl_pkey_get_private("file://".$privateKeyPath);
if (!$privateKey) die("Ошибка: не удалось загрузить приватный ключ\n");

openssl_sign($unsignedToken, $signature, $privateKey, OPENSSL_ALGO_SHA256);
$jwt = $unsignedToken . "." . base64url_encode($signature);

// ==== ЗАПРОС ACCESS TOKEN ====
$postFields = http_build_query([
    "grant_type" => "urn:ietf:params:oauth:grant-type:jwt-bearer",
    "assertion"  => $jwt,
    "client_id"  => $clientId,
    "scope"      => "read"
]);

$ch = curl_init($audience);
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => $postFields,
    CURLOPT_HTTPHEADER => [
        "Content-Type: application/x-www-form-urlencoded"
    ],
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_TIMEOUT => 30
]);

$response = curl_exec($ch);
if (!$response) die("Ошибка запроса токена: " . curl_error($ch));
curl_close($ch);

$data = json_decode($response, true);
if (!isset($data['access_token'])) die("Не удалось получить access token");
$accessToken = $data['access_token'];

echo "Access Token получен: " . substr($accessToken, 0, 20) . "...<br><br>";

// ==== ПОЛУЧЕНИЕ QR-КОДА ====
$qrUrl = "https://c2b-cert.sngb.ru/api/merchant/v1/qrc-data?mediaType=image/png&width=300&height=300";

$qrData = [
    "account" => $account,
    "merchantId" => $merchantId,
    "templateVersion" => "01",
    "qrcType" => "02",
    "amount" => 10000, // 100 рублей в копейках
    "currency" => "RUB",
    "qrcTtl" => 60,
    "paymentPurpose" => "Оплата тестовой услуги",
    "params" => [
        "paymentData" => "Тестовый платеж"
    ]
];

echo "Отправляем запрос на: " . $qrUrl . "<br>";
echo "Данные: " . json_encode($qrData, JSON_UNESCAPED_UNICODE) . "<br><br>";

$ch = curl_init($qrUrl);
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($qrData),
    CURLOPT_HTTPHEADER => [
        "Content-Type: application/json",
        "Authorization: Bearer $accessToken"
    ],
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_TIMEOUT => 30
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

echo "HTTP Code: " . $httpCode . "<br>";
echo "CURL Error: " . $curlError . "<br>";
echo "Response: " . $response . "<br><br>";

if ($httpCode == 201 && $response) {
    $result = json_decode($response, true);
    
    if (isset($result['image']['content'])) {
        // Получили изображение в base64
        $qrBase64 = $result['image']['content'];
        $mediaType = $result['image']['mediaType'] ?? 'image/png';
        
        echo "<h2>QR-код для оплаты:</h2>";
        echo "<img src='data:$mediaType;base64,$qrBase64' alt='QR-код'>";
        echo "<p>Payload: " . htmlspecialchars($result['payload']) . "</p>";
        echo "<p>QRC ID: " . htmlspecialchars($result['qrcId']) . "</p>";
    } else {
        echo "<h3>Полный ответ сервера:</h3><pre>";
        print_r($result);
        echo "</pre>";
    }
} else {
    echo "<strong>Ошибка получения QR-кода</strong>";
}
?>

