<?php
/**
 * Template Name: Spec Sel Page
 *
 * @package polidemika
 */

get_header();
?>
<style>
	.team { justify-content: start; }
	.team__card .card-info__position { overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
	
	.diw_h5 { font-weight: 500; font-size: 14px; line-height: 17.07px; letter-spacing: 2px; color: #AEB3C0; display: flex; justify-content: space-between; margin-bottom: 20px; margin-top: 20px; }
	.diw_oio_wrap { margin-bottom: 10px; }
	.diw_oio_row { margin-bottom: 20px; }
	.diw_oio_bubble { color: #fff; background: rgba(116, 172, 251, 1); padding: 5px 34px; margin-right: 20px; border-radius: 12px; }
	.diw_table { background: #fff; box-shadow: 0px 20px 30px 0px rgba(0, 0, 0, 0.05); padding: 16px 0px; margin-bottom: 80px; border-radius: 12px; }
	.diw_table_row { display: grid; grid-template-columns: 3fr 1fr; gap: 16px; padding: 10px 34px; }
	.diw_table_row:nth-child(2n) { background: rgba(116, 172, 251, 0.05); }
	.diwtr_price { text-align: right; }

	.bigcat_name { margin-bottom: 40px; font-size: 32px; font-weight: 800; line-height: 39.01px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; }
	.post-thumbnail { margin-bottom: 40px; }
	.title_cat { color: rgba(174, 179, 192, 1); font-size: 16px; font-weight: 600; line-height: 19.5px; letter-spacing: 2px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; margin-bottom: 20px; }
	.entry-header { background: transparent; } 
	.entry-title { margin: 0 0 14px; font-size: 24px; font-weight: 800; line-height: 29.26px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; }
	.entry-content { margin-bottom: 60px; }
</style>
      
<?php
/**
 * Специалисты на главной
 */

$or_el = get_field('dolzhnost_ili_po_odnomu');

if ($or_el == 'dol') {

$term_id = get_field('vybor_po_dolzhnosti');

$args = array(
  'post_type'      => 'doctor',
  'orderby' => 'title',
  'order' => 'ASC',
    'tax_query' => array(
        array(
            'taxonomy' => 'position',        
            'terms'    => $term_id,     
        ),
    ),
  'posts_per_page' => -1
);

$doctors = get_posts( $args );
}

if ($or_el == 'one') {
	$doctors = get_field('vybor_doktorov');
}
?>

<div class="container mt20" id="specialists"> 
    <div class="block-name">Наша комманда</div>
    <div class="block-f">
        <div class="block-title"><h1 class="entry-title"><?php the_title(); ?></h1></div>
<!--form>
  <div class="__select" data-state="">
    <div id="__select__title_id" class="__select__title" data-default="Выберите направление">Выберите направление</div>
    <div class="__select__content">
<?php
$categories = get_categories( [
	'taxonomy' => 'position',
	'orderby' => 'name',
	'order' => 'ASC'
] );
$i == 0;
foreach( $categories as $category ){ 
	$i++; ?>
	<input id="singleSelect<?php echo $i; ?>" class="__select__input" type="radio" name="singleSelect" />
	<label for="singleSelect<?php echo $i; ?>" class="__select__label" data-catid="<?php echo $category->term_id; ?>"><?php echo $category->name; ?></label>
<?php } ?>
    </div>
  </div>
</form-->		
    </div>
    <div class="team" style="">
        <?php foreach ( $doctors as $doctor ) : ?>
            <div class="team__card" data-term="<?php echo get_first_doctor_position( $doctor->ID )->term_id; ?>">
                <div class="card-foto" style="position: relative;">
<?php if (get_field('stazh', $doctor->ID)) { ?>
					<div class="staje" style="position: absolute; top: 0; right: 13px; width: 71px; height: 78px; box-sizing: border-box; padding: 6px 0 10px; background: #fff; box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.05); display: flex; flex-direction: column; align-items: center; border-radius: 0 0 8px 8px;">
						<img style="width: 24px; height: 24px; margin-bottom: 8px;" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/staje.svg">
						<div style="text-align: center; line-height: 1.2">
						<span style="color: rgba(174, 179, 192, 1)">опыт</span><br>
<?php  
$originalDate = get_field('stazh', $doctor->ID);
$dateObj = DateTime::createFromFormat('d/m/Y', $originalDate);
$newDate = $dateObj->format('Y-m-d');
				
$startDate = new DateTime($newDate);
$currentDate = new DateTime();

$interval = $currentDate->diff($startDate);

$years = $interval->y;
$months = $interval->m;

echo plural($years, ['год', 'года', 'лет']);
?>
						</div>
					</div>
<?php } ?>
                    <img src="<?php echo get_doctor_photo( $doctor->ID ); ?>">
                </div>
                <div class="card-info"> 
                    <div class="card-info__text">
                    <div class="card-info__fio">
                        <?php echo get_doctor_fio( $doctor->ID ); ?>
                    </div>
                    <div class="card-info__rating">
                    <div class="stars"> </div>
                    <div class="points">4.8</div>
                    </div>
                    </div>
					<div class="card-info__position">
                        <?php
                            $position = get_first_doctor_position( $doctor->ID );
                            if ( $position ) {
                                echo $position->name;
                            }
                        ?>
                        
                    </div>
                    
                </div>
                <div class="card-buttons">
                    <?php
                        $hashItems = array();
                        // Код направления
                        if($doctor_position = term_description($position)) {
                            $doctor_position = strip_tags($doctor_position);
                            $hashItems[] = "p:{$doctor_position}";
                        }
                        // Код врача
                        if($doctor_code = get_post_meta($doctor->ID, 'code', true)) {
                            $doctor_code = str_pad($doctor_code, 11, '0', STR_PAD_LEFT);
                            $hashItems[] = "d:{$doctor_code}";
                        }
                    ?>
                    <a href="<?php echo home_url(); ?>/appointment/#<?= implode('_', $hashItems); ?>">
                        <button><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/appointment-blue.svg" alt="">Записаться на прием</button>
                    </a>
                    <a class="card-link" href="<?php echo $doctor->guid; ?>">Подробнее</a>
                </div>
            </div>
        <?php endforeach; ?>
        
    </div>
<!--script type="text/javascript">
const selectSingle = document.querySelector('.__select');
const selectSingle_title = selectSingle.querySelector('.__select__title');
const selectSingle_labels = selectSingle.querySelectorAll('.__select__label');

selectSingle_title.addEventListener('click', () => {
    if ('active' === selectSingle.getAttribute('data-state')) {
        selectSingle.setAttribute('data-state', '');
    } else {
        selectSingle.setAttribute('data-state', 'active');
    }
});

const hashID = (window.location.hash.match(/^#(\d+)$/) ?? [,''])[1];
var elementss = document.getElementsByClassName('team__card');
for (let i = 0; i < selectSingle_labels.length; i++) {
    let catID = selectSingle_labels[i].getAttribute('data-catid');
    selectSingle_labels[i].addEventListener('click', (evt) => {
        selectSingle_title.textContent = evt.target.textContent;
        for (let elem of elementss) {
            elem.style.display = "flex";
            if (elem.getAttribute('data-term') != catID) {
                elem.style.display = "none";
            }	
        }
        window.location.hash = '#' + catID;
        selectSingle.setAttribute('data-state', '');
    });
    if(hashID == catID) {
        selectSingle_labels[i].click();
    }
}

document.querySelector('.team').style.display = "flex";
</script-->
	
	<div class="diw_description" id="uslugi">
						<div class="block-title">Услуги и цены</div>
						<div class="diw_h5"><span>Название услуги</span><span>Стоимость, рублей</span></div>
                        <div class="diw_table">
                        <?php
						$doc_id = get_field('uslugi_doktora');
                        $doctor_code = get_post_meta( $doc_id, 'code', true );
                        $doctor_code = str_pad( $doctor_code, 11, '0', STR_PAD_LEFT );
                        $services    = get_doctor_services( $doctor_code );

                        if ( $services && is_array( $services ) ) {
                            foreach ( $services as $service ) {
                                echo '<div class="diw_table_row">';
                                echo '<div>' . esc_html( $service['name'] ) . '</div>';
                                echo '<div class="diwtr_price">' . number_format( (float) $service['price'], 0, ',', ' ' ) . '&nbsp;₽</div>';
                                echo '</div>';
                            }
                        } else {
                            echo '<div class="diw_table_row"><div>Нет доступных услуг</div><div class="diwtr_price">—</div></div>';
                        }
                        ?>
                        </div>
                    </div>

<?php wp_reset_postdata(); ?>


<article>
	
	<?php if(get_field('kartinka_stati')) { ?>
<div class="post-thumbnail">
				<img width="1180" height="488" src="<?php the_field('kartinka_stati') ?>" class="attachment-post-thumbnail size-post-thumbnail wp-post-image" alt="" decoding="async">
</div>
	<?php } ?>
	
	<header class="entry-header">

<h2 class="entry-title"><?php the_title(); ?></h2>

	</header><!-- .entry-header -->

	<div class="entry-content">
<?php the_content(); ?>
	</div><!-- .entry-content -->

</article>

	<div class="conts_form">
		<div class="cf_conts">
			<div class="cfc_wrap">
				<?php the_field('sami_kontakty', 887); ?>
				Наши соцсети<br>
				<div class="cf_soc_seti">
					<a href="<?php the_field('ssylka_vk', 887); ?>"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/vk_w.svg"></a>
					<a href="<?php the_field('ssylka_tg', 887); ?>"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/tg_w.svg"></a>
					<a href="<?php the_field('ssylka_yut', 887); ?>"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/yt_w.svg"></a>
				</div>
			</div>
		</div>
		<div class="cf_form">
			<?php echo do_shortcode('[contact-form-7 id="bf9b26f" title="Контактная форма 1"]'); ?>
		</div>
	</div>
	
	
</div>

					


    </div>
<?php
get_footer();