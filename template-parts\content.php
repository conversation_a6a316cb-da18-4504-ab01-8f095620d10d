<?php
/**
 * Template part for displaying posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package polidemika
 */

?>

<style>
	.bigcat_name { margin-bottom: 40px; font-size: 32px; font-weight: 800; line-height: 39.01px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; }
	.single_news .post-thumbnail { margin-bottom: 40px; width: 100%; }
	.title_cat { color: rgba(174, 179, 192, 1); font-size: 16px; font-weight: 600; line-height: 19.5px; letter-spacing: 2px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; margin-bottom: 20px; }
	.entry-header { background: transparent; } 
	.entry-title { margin: 0 0 14px; font-size: 24px; font-weight: 800; line-height: 29.26px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; }
	.entry-content { margin-bottom: 60px; }
</style>

	<div class="bigcat_name">Новости</div>

<div class="single_news" id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
	
		<?php polidemika_post_thumbnail(); ?>
	
	<header class="entry-header">
		<div class="title_cat">
		<?php 
$categories = get_the_category();
if ( ! empty( $categories ) ) {
	echo esc_html( $categories[0]->name );
}
		?>
		</div>
		<?php
		if ( is_singular() ) :
			the_title( '<h1 class="entry-title">', '</h1>' );
		else :
			the_title( '<h2 class="entry-title"><a href="' . esc_url( get_permalink() ) . '" rel="bookmark">', '</a></h2>' );
		endif;

		if ( 'post' === get_post_type() ) :
			?>
			<div class="entry-meta">
				<?php
			//	polidemika_posted_on();
			//	polidemika_posted_by();
				?>
			</div><!-- .entry-meta -->
		<?php endif; ?>
	</header><!-- .entry-header -->

	<div class="entry-content">
		<?php
		the_content(
			sprintf(
				wp_kses(
					/* translators: %s: Name of current post. Only visible to screen readers */
					__( 'Continue reading<span class="screen-reader-text"> "%s"</span>', 'polidemika' ),
					array(
						'span' => array(
							'class' => array(),
						),
					)
				),
				wp_kses_post( get_the_title() )
			)
		);

		wp_link_pages(
			array(
				'before' => '<div class="page-links">' . esc_html__( 'Pages:', 'polidemika' ),
				'after'  => '</div>',
			)
		);
		?>
	</div><!-- .entry-content -->

	<footer class="entry-footer">
		<?php //polidemika_entry_footer(); ?>
	</footer><!-- .entry-footer -->
</div><!-- #post-<?php the_ID(); ?> -->

<?php
$args = array(
	'posts_per_page' => 3,
	'orderby' => 'rand'
);
$query = new WP_Query( $args );
if ( $query->have_posts() ) { ?>
<h2 class="page-title">Сейчас читают</h2>
	<div id="now_reading">
	<?php while ( $query->have_posts() ) {
		$query->the_post();
		get_template_part( 'template-parts/content_arch', get_post_type() );
	} ?>
	</div>
<?php }
else {
	// Постов не найдено
}
wp_reset_postdata();
?>
