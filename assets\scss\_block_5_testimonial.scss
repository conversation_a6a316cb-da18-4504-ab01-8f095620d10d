.testimonial{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 40px;
    margin-bottom: 20px;

    &__card{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        
        height: 340px;
        width: 380px;
        border-radius: 12px;
        background-color: #fff;
        padding: 20px;

        box-shadow: 0px 20px 30px 0px #0000000D;


        .t-card {
            &__top {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                padding: 10px;
            }

            &__person {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: flex-start;
            }

            &__photo {
                height: 60px;
                width: 60px;
                margin-right: 10px;
            }

            &__title {
                @extend %font_600;
                font-size: 16px;
                width: 150px;
                line-height: 1.2;
            }

            &__name {
                min-height: 40px;
            }

            &__date {
                font-size: 15px;
                color: #AEB3C0;
            }

            &__rating {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: flex-start;
            }

            &__logo {
                width: 36px;
                height: 36px;
                margin-bottom: 4px;
            }

            &__points {
                @extend %font_600;
                font-size: 16px;

                &::before {
                    content: "★";
                    color: #FFC500;
                    margin-right: 4px;
                }
            }

            &__text {
                @extend %font_500;
                font-size: 16px;
                padding: 10px;
            }
        }
    }
}

button.white {
    background-color: #fff;
    border-radius: 12px;
    width: 44px;
    height: 40px;
    border:none;
    background-position: center;
    background-repeat: no-repeat;

    &:focus {
        outline: none;
    }
}

.testimonial-nav {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 40px;

    &__gray {
        background-color: #AEB3C0;
        height: 6px;
        width: 20px;
        border-radius: 30px;
    }
    &__blue {
        background-color: #0F61D9;
        height: 6px;
        width: 60px;
        border-radius: 30px;
    }
}