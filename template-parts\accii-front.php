<style>
	.team { justify-content: left; }
	.team__card.accii { height: 650px; }
	.acc_wrap { display: flex; flex-direction: column; justify-content: space-between; width: 100%; height: 100%; padding: 20px 24px 30px; background-color: rgba(243, 244, 247, 1); background-position: center bottom; background-size: 100% auto; background-repeat: no-repeat; border-radius: 12px; }
	.aw_s_title { font-size: 14px; font-weight: 500; line-height: 17.07px; letter-spacing: 2px; text-align: center; text-underline-position: from-font; text-decoration-skip-ink: none; margin-bottom: 24px; color: rgba(174, 179, 192, 1); }
	.aw_l_title { font-family: Montserrat; font-size: 18px; font-weight: 700; line-height: 21.94px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; margin-bottom: 16px; color: rgba(1, 20, 36, 1); }
	.aw_l_title span { color: rgba(15, 97, 217, 1); }
	.aw_list {  }
	.awl_item { background: #fff; padding: 6px 8px; border-radius: 12px; margin-bottom: 10px; width: fit-content; }
	.awl_item img { margin-right: 5px; }
	.main-banner__button { text-align: center; }
	.main-banner__button button { padding: 15px 46px; }
</style>
      
<?php

$args = array(
  'post_type'      => 'accii',
  'orderby' => 'date',
  'order' => 'ASC',
//    'tax_query' => array(
//        array(
//            'taxonomy' => 'podrazdelenie', 
//            'field'    => 'name',         
//            'terms'    => '00-000001',     
//        ),
//    ),
  'posts_per_page' => 3//-1
);

$accii = get_posts( $args );

?>

<div class="container mt20" id="specialists"> 
    <div class="block-f">
        <div class="block-title accii">Акции</div>
    </div>
    <div class="team" style="margin-bottom: 40px;">
        <?php foreach ( $accii as $acc ) : ?>
            <div class="team__card accii">
				<div class="acc_wrap" style="background-image: url('<?php the_field('fonovoe_izobrazhenie' ,$acc->ID) ?>');">
					<div>
					<div class="aw_s_title"><?php the_field('srok' ,$acc->ID) ?></div>
					<div class="aw_l_title"><?php the_field('zagolovok' ,$acc->ID) ?></div>
					<div class="aw_list">
						<?php
						if( have_rows('punkty' ,$acc->ID) ):
    						while ( have_rows('punkty' ,$acc->ID) ) : the_row(); ?>
					 			<div class="awl_item"><img src="https://polymedica86.com/wp-content/themes/polidemika/assets/images/heart-circle.svg" /><?php the_sub_field('tekst_punkta'); ?></div>
    						<?php endwhile;
						else :
						endif;
						?>
					</div>
					</div>
					<div class="main-banner__button home-visit">
              			<a href="https://polymedica86.com/appointment">
                			<button><img src="https://polymedica86.com/wp-content/themes/polidemika/assets/images/phone_w_acc.svg" alt="">Жду звонка</button>
              			</a>
            		</div>
				</div>
            </div>
        <?php endforeach; ?>
    </div>
    <div class="block-link" style="text-align: center;"><a href="/akczii/">Все акции</a></div>
</div>
<? wp_reset_postdata(); ?>