<?php
defined( 'ABSPATH' ) || die();

function get_doctor_fio( $id ) {
	$fio = get_post_meta( $id, 'fio', true );

	if ( $fio ) {
		return $fio;
	}

	return '';

	// $title = get_title( $id );
	// $words = explode( ' ', $title );
	// $first_three_words = array_slice( $words, 0, 3 );
	// $fio = implode( ' ', $first_three_words );

	// return $fio;
}

function get_doctor_photo( $id ) {
	$photo = get_field( 'photo', $id );

	if ( $photo ) {
		return $photo;
	}

	return get_stylesheet_directory_uri() . '/assets/images/no_photo.jpg';
}

function get_doctor_from_code( $code ) {

		$args = array(
			'post_type'      => 'doctor',
			'meta_key'       => 'code',
			'meta_value'     => (int) $code,
			'posts_per_page' => 1,
			'no_found_rows'  => true,
			'published'      => true,
		);

		$doctors = get_posts( $args );

		if ( ! empty( $doctors ) ) {
			return $doctors[0];
		}

		return null;
}



function delete_all_doctors() {
	$args = array(
		'post_type'      => 'doctor',
		'posts_per_page' => -1,
		'post_status'    => 'any',
	);

	$doctors = get_posts( $args );

	if ( ! empty( $doctors ) ) {
		foreach ( $doctors as $doctor ) {
			wp_delete_post( $doctor->ID, true );
		}
		echo 'Все доктора удалены.';
	} else {
		echo 'Доктора не найдены.';
	}
}

function get_first_doctor_position( $doctor_id ) {
	$positions = wp_get_post_terms( $doctor_id, 'position' );

	if ( ! is_wp_error( $positions ) && ! empty( $positions ) ) {
		return $positions[0];
	}

	return null;
}

function get_all_positions() {

	$args = array(
		'taxonomy'   => 'position',
		'hide_empty' => false,
	);

	return get_terms( $args );
}

add_action( 'wp_ajax_get_schedules', 'get_schedules_handler' );
add_action( 'wp_ajax_nopriv_get_schedules', 'get_schedules_handler' ); // Для неавторизованных пользователей

function get_schedules_handler() {
	$position_code = isset( $_POST['position_code'] ) ? sanitize_text_field( $_POST['position_code'] ) : '';
	$date          = isset( $_POST['date'] ) ? sanitize_text_field( $_POST['date'] ) : ''; // Получаем дату

	if ( empty( $position_code ) || empty( $date ) ) {
		wp_send_json_error( 'Не переданы необходимые параметры position_code или date' );
		return;
	}

	$result = get_schedules( $position_code, $date );
	wp_send_json( $result );
}

function get_term_by_position_code( $position_code ) {
	$term_query = new WP_Term_Query(
		array(
			'taxonomy'          => 'position',
			'hide_empty'        => false,
			'description__like' => $position_code, // Ищем в описании
		)
	);

	if ( ! empty( $term_query->terms ) && ! is_wp_error( $term_query ) ) {
		foreach ( $term_query->terms as $term ) {
			if ( (int) $term->description === (int) $position_code ) {
				return $term;
			}
		}
	}

	return null;
}


function get_doctor_services_from_taxonomy( $doctor_id ) {
	$services = array();

	// Получаем термины услуг для врача
	$service_terms = wp_get_post_terms( $doctor_id, 'service' );

	if ( ! is_wp_error( $service_terms ) && ! empty( $service_terms ) ) {
		foreach ( $service_terms as $term ) {
			$services[] = array(
				'code'           => $term->description, // код хранится в description
				'name'           => $term->name,
				'execution_time' => get_term_meta( $term->term_id, 'execution_time', true ),
				'is_primary'     => get_term_meta( $term->term_id, 'is_primary', true ),
				'price'          => get_term_meta( $term->term_id, 'price', true ),
			);
		}
	}

	return $services;
}

function get_doctor_services( $doctor_code, $force_update = false ) {

	$doctor = get_doctor_from_code( $doctor_code );

	if ( ! $doctor ) {
		return null;
	}

	// Сначала пробуем получить сервисы из таксономии
	$services = get_doctor_services_from_taxonomy( $doctor->ID );
	$last_update = get_post_meta( $doctor->ID, 'services_last_update', true );

	// Проверяем необходимость обновления:
	// - если принудительное обновление
	// - если нет сервисов
	// - если прошло больше суток с последнего обновления
	if ( $force_update ||
		empty( $services ) ||
		empty( $last_update ) ||
		( time() - strtotime( $last_update ) > DAY_IN_SECONDS )
	) {
		$services = get_doctor_servises_from_api( $doctor_code );
	} else {
		// Если есть актуальные данные в таксономии, используем их
		$services = get_doctor_services_from_taxonomy( $doctor->ID );
	}

	return $services;
}

add_action( 'wp_ajax_get_doctor_info', 'get_doctor_info_handler' );
add_action( 'wp_ajax_nopriv_get_doctor_info', 'get_doctor_info_handler' );

function get_doctor_info_handler() {
	$doctor_id = isset( $_POST['doctor_id'] ) ? sanitize_text_field( $_POST['doctor_id'] ) : '';

	if ( empty( $doctor_id ) ) {
		wp_send_json_error( 'Не передан doctor_id' );
		return;
	}

	$doctor = get_doctor_from_code( $doctor_id );

	if ( ! $doctor ) {
		wp_send_json_error( 'Доктор не найден' );
		return;
	}

	$response = array(
		'id'       => $doctor->ID,
		'photo'    => get_doctor_photo( $doctor->ID ),
		'fio'      => get_doctor_fio( $doctor->ID ),
		'position' => get_first_doctor_position( $doctor->ID ),
		'rating'   => get_field( 'score', $doctor->ID ) ?: '0',
	);

	wp_send_json_success( $response );
}
