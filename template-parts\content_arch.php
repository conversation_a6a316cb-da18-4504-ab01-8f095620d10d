<?php
/**
 * Template part for displaying posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package polidemika
 */

?>

<style>
	article { display: flex; justify-content: space-between; background: #fff; padding: 20px; margin-bottom: 30px; border-radius: 12px; }
	#now_reading article { display: block; width: 33%; }
	.post-thumbnail { width: 30%; border-radius: 12px; overflow: hidden; }
	.post-thumbnail img { width: 100%; height: 100%; object-fit: cover; }
	#now_reading .post-thumbnail { aspect-ratio: 4 / 3; display: block; width: 100%; margin-bottom: 20px; }
	.title_cat { color: rgba(174, 179, 192, 1); font-size: 16px; font-weight: 600; line-height: 19.5px; letter-spacing: 2px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; margin-bottom: 20px; }
	.entry-header { background: transparent; } 
	.entry-title { margin: 0 0 14px; font-size: 24px; font-weight: 800; line-height: 29.26px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; }
	#now_reading .entry-title { font-size: 18px; font-weight: 600; }
	.entry-title a { color: inherit; }
	.entry-content, .entry-content p { margin-bottom: 0px; }
	#now_reading .entry-content, #now_reading .entry-content p { font-size: 14px; }
	.arch_one_wrap { width: 66%; padding-left:20px;}
	#now_reading .arch_one_wrap { width: 100%; }
	.read_more { margin-top: 20px; display: block; }
</style>

<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
	
		<?php polidemika_post_thumbnail(); ?>
<div class="arch_one_wrap">	
	<header class="entry-header">
		<div class="title_cat">
		<?php 
$categories = get_the_category();
if ( ! empty( $categories ) ) {   
	echo esc_html( $categories[0]->name );
}
		?>
		</div>
		<?php
		if ( is_singular() ) :
			the_title( '<h1 class="entry-title">', '</h1>' );
		else :
			the_title( '<h2 class="entry-title"><a href="' . esc_url( get_permalink() ) . '" rel="bookmark">', '</a></h2>' );
		endif;

		if ( 'post' === get_post_type() ) :
			?>
			<div class="entry-meta">
				<?php
			//	polidemika_posted_on();
			//	polidemika_posted_by();
				?>
			</div><!-- .entry-meta -->
		<?php endif; ?>
	</header><!-- .entry-header -->

	<div class="entry-content">
		<?php the_excerpt(); ?>
	</div><!-- .entry-content -->
</div>
	<footer class="entry-footer">
		<?php //polidemika_entry_footer(); ?>
	</footer><!-- .entry-footer -->
</article><!-- #post-<?php the_ID(); ?> -->
