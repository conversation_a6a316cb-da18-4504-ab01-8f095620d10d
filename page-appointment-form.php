<?php
/**
 * The template for displaying the front page.php
 *
 * @package polidemika
 */
if (! $_SERVER['REQUEST_METHOD'] === 'POST') {
  wp_redirect(home_url('/'));
}
get_header();
?>
      <div class="container"> 
        <div class="block-name center mt20">Запись</div>
        <div class="appointment" id="appointment-form">
          <div class="block-title center" style="text-align: center; width: 100%">Записаться на прием к врачу</div>
            <div class="f-between" style="align-items: flex-start">
              <div class="appointment__block">
              <?php
                  $birthDate   = $_POST['birthDate'] ?? '';
                  $doctorId    = $_POST['doctorId'] ?? '';
                  $serviceCode = $_POST['serviceCode'] ?? '';
                  $serviceName = $_POST['serviceName'] ?? '';
                  $servicePrice = $_POST['servicePrice'] ?? '';
                  $workPlace   = $_POST['work_place'] ?? '';
                  $date        = $_POST['date'] ?? '';
                  $time        = $_POST['time'] ?? '';
                  
                  $doctor = get_doctor_from_code($doctorId);
                  ?>
                  <div class="team">
                      <div class="team__card" style="box-shadow: none">
                          <div class="card-foto">
                              <img src="<?php echo get_doctor_photo( $doctor->ID ); ?>">
                          </div>
                          <div class="card-info"> 
                              <div class="card-info__text">
                              <div class="card-info__fio">
                                  <?php echo get_doctor_fio( $doctor->ID ); ?>
                              </div>
							  <div class="card-info__rating">
                              <div class="stars"> </div>
                              <div class="points"><?php echo ( get_field( 'score', $doctor->ID ) ) ? get_field( 'score', $doctor->ID ) : '0'; ?></div>
                              </div>
                              </div>
							  <div class="card-info__position">
                                  <?php
                                      $position = get_first_doctor_position( $doctor->ID );
                                      if ( $position ) {
                                          echo '<a href="' . get_term_link( $position ) . '">' . $position->name . '</a>';
                                      }
                                  ?>
                                  
                              </div>
                              
                          </div>
                      </div>
              </div>

              </div>
              <div class="appointment__block">

              <form action="sended" method="POST" class="mt20">
                  <div class="appointment__info">
                    <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/clock.svg"><?php echo $date . ' в ' . $time; ?> 
                  </div>
                  <div class="appointment__info">
                    <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/clipboard.svg"><?php echo $serviceName; ?>
                  </div>
                  <input type="text" id="last_name" name="last_name" placeholder="Введите фамилию" required>
                  <input type="text" id="first_name" name="first_name" placeholder="Введите имя" required>
                  <input type="text" id="middle_name" name="middle_name" placeholder="Введите отчество" required>
                  <input type="tel" id="phone" name="phone" placeholder="Введите телефон" required>

                      <!-- Скрытые поля для дополнительных данных -->
                  <input type="hidden" name="birthDate" value="<?php echo $birthDate; ?>">
                  <input type="hidden" name="doctorId" value="<?php echo $doctorId; ?>">
                  <input type="hidden" name="serviceCode" value="<?php echo $serviceCode; ?>">
                  <input type="hidden" name="serviceName" value="<?php echo $serviceName; ?>">
                  <input type="hidden" name="servicePrice" value="<?php echo $servicePrice; ?>">
                  <input type="hidden" name="work_place" value="<?php echo $workPlace; ?>">
                  <input type="hidden" name="date" value="<?php echo $date; ?>">
                  <input type="hidden" name="time" value="<?php echo $time; ?>">
				  
				  <div id="mypol">
<style>
	#submitForm.dis { opacity: .75; pointer-events: none; cursor: default; }
	#mypol { display: flex; align-items: start; gap: 10px; }
</style>
					  <input style="width: 5%; margin-top: 4px;" type="checkbox" id="politics" onclick="mycheck();" value="" autocomplete="off"/>
						<div style="width: 95%;">
							Согласие с <a href="/soglasie-na-obrabotku-personalnyh-dannyh/">обработкой персональных данных</a> и <a href="/privacy-policy/">политикой конфиденциальности</a>.
					  </div>
<script type='text/javascript'>
function mycheck() {
var submit = document.getElementById('submitForm');
		console.log(submit);
if (document.getElementById('politics').checked)
submit.classList.remove('dis');
else
submit.classList.add('dis');
}
</script>
				  </div>

                  <button type="submit" class="submit_button dis" id="submitForm"><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/appointment.svg" alt="">Отправить</button>
                </form>

              </div>
            </div>


            </div>
        </div>
      </div>
<?php
get_footer();
