.services {
    margin-top: 40px;
    margin-bottom: 80px;
    width: 100%;
    background-color: #fff;
    border-radius: 12px;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
    position: relative;

    padding: 40px 80px;

    box-shadow: 0px 20px 30px 0px #0000000D;


    &__tags {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 10px;

        .tag {
            background-color: #F3F4F7;
            border-radius: 12px;
            padding: 10px 30px 10px 10px;
            background-image: url(../images/check.svg);
            background-repeat: no-repeat;
            background-position: right 10px center;
            cursor: pointer;
            user-select: none; 

            &.selected {
                background-color: #74ACFB;
                color: #fff;
                background-image: url(../images/check_white.svg);
            }
        }
    }

    &__list {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        justify-content: space-between;
        flex-wrap: wrap;

        ul {
            list-style: none;
            margin: 0;
            padding: 0;
            column-count: 3; /* Разделить на три колонки */
            column-gap: 20px; /* Промежуток между колонками */

            li {
                cursor: pointer;
                break-inside: avoid; /* Избегать разрывов внутри элемента */

                a {
                    text-decoration: none;
                    color: $color_text;
                    text-transform: capitalize; 

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
    }

    &__nav {
        position: absolute;
        bottom: 40px;
        right: 80px;
        display: flex;
        display: none;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;

        button {
            width: 44px;
            height: 40px;
            background-color: #F3F4F7;
            border: none;
            border-radius: 12px;
            background-position: center;
            background-repeat: no-repeat;
        }
    }
}

.left-arrow {
    background-image: url(../images/nav-left-arrow.svg);
}

.right-arrow {
    background-image: url(../images/nav-right-arrow.svg);
}