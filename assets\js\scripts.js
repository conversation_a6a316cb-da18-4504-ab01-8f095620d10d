const calendarState = {
    currentMonth: new Date().getMonth() + 1,
    currentYear: new Date().getFullYear(),
    currentDay: 1,
};

let dataSet = null;
let isCalendaReseting = false;
let isChildClient = false;

document.addEventListener('DOMContentLoaded', function() {
    // Вопрос-Ответ
    const arrows = document.querySelectorAll('.faq__card .arrow');
    arrows.forEach(arrow => {
      arrow.addEventListener('click', function() {
		  arrows.forEach(arrow => {
				arrow.closest('.faq__card').classList.remove('active');
		  });
        const faqCard = this.closest('.faq__card');
        faqCard.classList.toggle('active');
      });
    });

    // Теги сервисов
    const servicesTags = document.querySelector('.services__tags');
    if (servicesTags) {
        const tags = servicesTags.querySelectorAll('.tag');
        const postition_lists  = document.querySelectorAll('.category_ul');
        tags.forEach(tag => {
            tag.addEventListener('click', function() {
                tags.forEach(unselect_tag => {
                    unselect_tag.classList.remove('selected');
                });

                tag.classList.add('selected');

                postition_lists.forEach( list => {
                    list.classList.add('hide');
                    if (tag.dataset.catid === list.dataset.catid) {
                        list.classList.remove('hide');
                    }                    
                });

            });
        });
    }

    // Запись к доктору
    const appointmentElement = document.getElementById('appointment');
    if (appointmentElement) {
      handleAppointments(appointmentElement);
    }

    // Обработка формы поиска
    searchFormHandler();
});

// Получить данные из якоря ссылки
function getHashData() {
    const hashArray = (window.location.hash.match(/^#(.+)$/) ?? [,''])[1].split('_');
    return hashArray.reduce(function(accumulator, value) {
        const item = value.split(':', 2);
        if(item[0]) {
            return { ...accumulator, [item[0]]: item[1] ?? null};
        }
        return { ...accumulator };
    }, {});
}

// Получить значение ключа из данных якоря
function getHashKey(key) {
    const hashData = getHashData();
    return hashData[key];
}

// Установить или удалить значение ключа данных якоря
function setHashKey(key, value) {
    let hashData = getHashData();
    if(value) {
        hashData[key] = value;
    } else if(Object.hasOwn(hashData, key)) {
        delete hashData[key];
    }
    
    let hashArray = [];
    for(const k in hashData) {
        hashArray.push(k + ':' + hashData[k]);
    }
    
    window.location.hash = '#' + hashArray.join('_');
}

function calculateAge(birthday) { // birthday is a date
    var ageDifMs = Date.now() - birthday.getTime();
    var ageDate = new Date(ageDifMs); // miliseconds from epoch
    return Math.abs(ageDate.getUTCFullYear() - 1970);
}

function handleAppointments(appointment) {
    const birthDateInput = appointment.querySelector('input[name="birth_date"]');
    const positionSelect = appointment.querySelector('select[name="position"]');
    //const doctorSelect = appointment.querySelector('select[name="doctor"]');
    
    // Создаем кастомный select
    const customSelect = document.createElement('div');
    customSelect.className = 'custom-select';
    customSelect.style.display = 'none';
	if (positionSelect.classList.contains('open_one')){
		customSelect.style.display = 'block';
	}
	
    // Создаем поле поиска (оно будет основным элементом)
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.className = 'custom-select__search';
    searchInput.placeholder = 'Поиск направления...';
    customSelect.appendChild(searchInput);
    
    // Создаем выпадающий список
    const optionsContainer = document.createElement('div');
    optionsContainer.className = 'custom-select__options';
    
    // Добавляем опции из оригинального select
    const optionsList = document.createElement('div');
	if (positionSelect.classList.contains('open_one')){
	optionsList.className = 'custom-select__options-list ggg';
	}
	else {
    optionsList.className = 'custom-select__options-list';
	}
    
    optionsContainer.appendChild(optionsList);
    customSelect.appendChild(optionsContainer);
    
    // Вставляем кастомный select после оригинального
    positionSelect.parentNode.insertBefore(customSelect, positionSelect.nextSibling);
    positionSelect.style.display = 'none';
    
    // Функция обновления опций
    function updateOptions() {
        optionsList.innerHTML = '';
        
        Array.from(positionSelect.options).forEach((option, index) => {
            if (index === 0) return; // Пропускаем первую опцию (placeholder)
            if (option.hidden) return; // Пропускаем скрытые опции
            
            const optionDiv = document.createElement('div');
            optionDiv.className = 'custom-select__option';
            optionDiv.textContent = option.textContent;
            optionDiv.dataset.value = option.value;
            optionDiv.dataset.children = option.dataset.children;
            
            optionDiv.addEventListener('click', () => {
                positionSelect.value = option.value;
                searchInput.value = option.textContent;
				if (optionsList.classList.contains('ggg')){
				searchInput.setAttribute('data-state', option.getAttribute('value'));
					
		
var elementss = document.getElementsByClassName('team__card');
let catID = option.getAttribute('value');

		for (let elem of elementss) {
			elem.style.display = "flex";
			if (elem.getAttribute('data-term') != catID) {
				elem.style.display = "none";
			}	
		}
					
				}
                searchInput.style.color = 'black';
                customSelect.classList.remove('open');
                
                // Вызываем событие change на оригинальном select
                const event = new Event('change');
                positionSelect.dispatchEvent(event);
            });
            
            optionsList.appendChild(optionDiv);
        });
    }
    
    // Обработчик поиска
    searchInput.addEventListener('input', (e) => {
        const searchText = e.target.value.toLowerCase();
        const options = optionsList.querySelectorAll('.custom-select__option');
        
        options.forEach(option => {
            const text = option.textContent.toLowerCase();
            option.style.display = text.includes(searchText) ? '' : 'none';
        });
        
        customSelect.classList.add('open');
    });
    
    // Открытие списка при фокусе на поле поиска
    searchInput.addEventListener('focus', () => {
        customSelect.classList.add('open');
        updateOptions();
    });
    
    // Закрытие при клике вне селекта
    document.addEventListener('click', (e) => {
        if (!customSelect.contains(e.target)) {
            customSelect.classList.remove('open');
        }
    });
    
    // Модифицируем обработчик изменения даты рождения
if (birthDateInput) {
    birthDateInput.addEventListener('change', function () {
        const dateString = this.value;
        this.style.color = (dateString ? 'black' : 'gray');
        const date = new Date(dateString);
        if (date instanceof Date && !isNaN(date) && date.getFullYear() > 1900) {
            const is_children = (calculateAge(date) < 18);
            isChildClient = is_children;
            positionSelect.querySelectorAll('option').forEach(function(elem) {
                if(children = elem.getAttribute('data-children')) {
                    if(is_children && children != 'true') {
                        elem.setAttribute('hidden', 'hidden');
                    } else {
                        elem.removeAttribute('hidden');
                    }
                }
            });
            customSelect.style.display = 'block';
            updateOptions();
            setHashKey('b', dateString);
            
            if(hashPosition = getHashKey('p')) {
                if(!positionSelect.querySelector('option[value="' + hashPosition + '"]').hasAttribute('hidden')) {
                    positionSelect.value = hashPosition;
                    searchInput.value = positionSelect.options[positionSelect.selectedIndex].textContent;
                    var changeEvent = new Event('change');
                    positionSelect.dispatchEvent(changeEvent);
                }
            }
        }
    });
}
    
    // Обработка изменения позиции
    positionSelect.addEventListener('change', function () {
        const positionCode = this.value;
        this.style.color = (positionCode ? 'black' : 'gray');
        if (!positionCode) return;
        setHashKey('p', positionCode);
        resetUI();
        get_schedules(positionCode);
    });
    
    // Обработка изменения доктора
    // doctorSelect.addEventListener('change', function () {
    //     if (!dataSet) return;
        
    //     doctorId = doctorSelect.value;
    //     if (!doctorId) return;
        
    //     this.style.color = (doctorId ? 'black' : 'gray');
        
    //     setHashKey('d', doctorId);
        
    //     resetUI();
        
    //     makeServiceSelect(doctorId, dataSet);

    //     // получаем время записи, где is_first: true
    //     const firstServiceCode = dataSet[doctorId][0].services.find(service => service.is_first === true)?.code || 0;

    //     dateSelect(doctorId, firstServiceCode, dataSet);
        
    //     // Скрываем время записи, если текущая дата недоступна
    //     const timeDiv = document.querySelector('#time_select');
    //     timeDiv.innerHTML = '';
    //     $('.ui-datepicker-current-day').click();
    // });
    
    // Устанавливаем дату рождения из данных якоря
    if(hashDate = getHashKey('b')) {
        birthDateInput.value = hashDate;
        var changeEvent = new Event('change');
        birthDateInput.dispatchEvent(changeEvent);
    }
    
}

// Получение списка врачей, услуг и свободного времени с сервера
function get_schedules(positionCode) {
    
    // модалка загрузки
    document.getElementById('overlay').style.display = 'block';
    document.getElementById('loadingModal').style.display = 'block';
    document.getElementById('appointment_container').classList.add('blurred'); // Блюрим фон
    
    // Отправка AJAX-запроса
    fetch(ajax_object.ajax_url, { 
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'get_schedules',
            position_code: positionCode,
            //date: get_current_calendar_date()
            date: get_today_date()
        }),
    })
    .then(response => response.json())
    .then(data => {
        // Успешное получение данных
        dataSet = data;
        console.log(data);
        //makeDoctorSelect(data);
        renderDoctorCards(data);
    })
    .catch(error => console.error('Ошибка:', error))
    .finally(() => {
        // Скрываем модальное окно и восстанавливаем фон
        document.getElementById('overlay').style.display = 'none';
        document.getElementById('loadingModal').style.display = 'none';
        document.getElementById('appointment_container').classList.remove('blurred'); // Убираем блюр
    });
}

function makeDoctorSelect(data) {
    data = dataSet;
    const doctorSelect = document.querySelector('.appointment select[name="doctor"]');
    
    doctorSelect.style.display = 'flex';
    
    // Проверяем, если дата null или отсутствует
    if (!data || data === null) {
        doctorSelect.disabled = true; // Делаем селектор неактивным
        doctorSelect.innerHTML = ''; // Очищаем список
        const noDoctorOption = document.createElement('option');
        noDoctorOption.disabled = true; // Делаем эту опцию недоступной для выбора
        noDoctorOption.selected = true;
        noDoctorOption.textContent = 'Нет свободной записи'; // Текст, который будет отображаться
        doctorSelect.appendChild(noDoctorOption);
        return; // Прерываем выполнение функции, если дата невалидна
    }
    
    
    doctorSelect.disabled = false;
    doctorSelect.innerHTML = '';
    const doctors = get_doctors(data);
    
    const titleOption = document.createElement('option');
    titleOption.disabled = true;
    titleOption.selected = true;
    titleOption.textContent = 'Выберите врача';
    doctorSelect.appendChild(titleOption);

    doctors.forEach(doctor => {
        const option = document.createElement('option');
        option.style.color = 'black';
        option.value = doctor["doctor_id"];
        option.textContent = doctor["fio"];
        doctorSelect.appendChild(option);
        // Устанавливаем доктора из данных якоря
        if(hashDoctor = getHashKey('d')) {
            if(option.value == hashDoctor) {
                doctorSelect.value = option.value;
                var changeEvent = new Event('change');
                doctorSelect.dispatchEvent(changeEvent);
            }
        }
    });
    
    if(doctorSelect.selectedIndex == 0) {
        setHashKey('d', null);
    }

}

function makeServiceSelect(doctorId, data) {
    document.querySelector('#datepicker').style.display = 'flex';
    const serviceSelect = document.querySelector('.appointment select[name="service"]');
    serviceSelect.style.display = 'flex';
    serviceSelect.disabled = false;
    serviceSelect.innerHTML = '';
    const services = get_services(doctorId, data);
    const titleOption = document.createElement('option');
    titleOption.disabled = true;
    titleOption.selected = true;
    titleOption.textContent = 'Выберите услугу';
    serviceSelect.appendChild(titleOption);

    serviceSelect.addEventListener('change', function () {
        const serviceCode = this.value;
        this.style.color = (serviceCode ? 'black' : 'gray');
        setHashKey('s', serviceCode);
        dateSelect(doctorId, serviceCode, data);
        $('.ui-datepicker-current-day').click();
    });

    services.forEach(service => {
        const option = document.createElement('option');
        option.style.color = 'black';
        option.value = service["code"];
        option.textContent = service["name"];
        serviceSelect.appendChild(option);
        // Устанавливаем услугу из данных якоря
        if(hashService = getHashKey('s')) {
            if(option.value == hashService) {
                serviceSelect.value = option.value;
                var changeEvent = new Event('change');
                serviceSelect.dispatchEvent(changeEvent);
            }
        }
    });

    if(serviceSelect.selectedIndex == 0) {
        setHashKey('s', null);
    }
}


function get_doctors(data) {
    const doctorKeys = Object.keys(data);
    let result = [];
    doctorKeys.forEach(doctorKey => {
        result.push({
            doctor_id:  data[doctorKey][0].doctor_id, // Ключ и значение
            fio: data[doctorKey][0].fio // ФИО врача
        });
    });
    return result;
}

function get_services(doctorId, data) {
    const services = new Array();
    
    // Добавим проверки
    if (!data || !data[doctorId]) {
        console.error('No data or doctor data not found');
        return services;
    }

    for(let item of data[doctorId]) {
        // Проверяем существование services и что это массив
        if (item && item.services && Array.isArray(item.services)) {
            for(let service of item.services) {
                if(!services.some((obj) => obj.code === service.code)) {
                    services.push(service);
                }
            }
        } else {
            console.warn('Services not found');
        }
    }
    
    return services;
}

function getDates(doctorId, data) {
    const result = [];
    const dates = data[doctorId];
    dates.forEach(date => {
        result.push({
            date: date.date,
            start_time: date.start_time,
            end_time: date.end_time,
            work_place: date.work_place
        });
    })
    return result;
}

function getTimesOneRow(doctorId, serviceCode, date, data) {
    const availableDates = data[doctorId];
    let time = "0:15:00";
    
    // Функция рассчета разницы во времени
    const getTimeDiff = function(start, end) {
        start = start.split(':');
        end = end.split(':');
        let startDate = new Date(0, 0, 0, start[0], start[1], start[2]);
        let endDate = new Date(0, 0, 0, end[0], end[1], end[2]);
        let diff = endDate.getTime() - startDate.getTime();
        let hours = Math.floor(diff / 1000 / 60 / 60);
        diff -= hours * 1000 * 60 * 60;
        let minutes = Math.floor(diff / 1000 / 60);
        diff -= hours * 1000 * 60;
        let seconds = Math.floor(diff / 1000);
        hours = (hours < 0 ? hours + 24 : hours);
        return ('00' + hours).slice(-2) + ':' + ('00' + minutes).slice(-2) + ':' + ('00' + seconds).slice(-2);
    }
    
    availableDates.forEach( availableDate => {
        if (availableDate.date === date.date) {
            if(serviceCode === 0) {
                //time = getTimeDiff(availableDate['start_time'], availableDate['end_time']);
                time = availableDate.services[0].time ? availableDate.services[0].time : time;
            } else {
                availableDate.services.forEach(service => {
                    if (service.code === serviceCode) {
                        time = service.time ? service.time : time;
                    }
                });
            }
        } 
    });
    console.log(doctorId, serviceCode, time);
    timeSlots = generateTimeSlots(date["start_time"], date["end_time"], time);
    
    // добавляем work_place к слоту времени
    const result = timeSlots.map(timeSlot => ({
        time: timeSlot,
        work_place: date["work_place"]
    }));
    
    
    
    return result;
}

function getTimes(doctorId, serviceCode, date, transformedData, data) {
    const result = [];
    transformedData[date].forEach(row => {
        result.push(...getTimesOneRow(doctorId, serviceCode, row, data));
    });
    return result;
}

function dateSelect(doctorId, serviceCode, data) {
    
    //const dates = getDates(doctorId, data);

    const doctorData = data[doctorId]; 

    if (!doctorData) {
        return {}; 
    }
    
    const current_time = new Date().getTime();
    const transformedData = doctorData.reduce((acc, { date, start_time, end_time, work_place }) => {
        const day = date; // День в формате "дд.мм.гггг"
        
        // Оставляем только будущие даты
        let days = day.split('.');
        let times = start_time.split(':');
        if(new Date(days[2], days[1] - 1, days[0], times[0], times[1], times[2]).getTime() > current_time) {
            if (!acc[day]) {
                acc[day] = [];
            }
            acc[day].push({ date, start_time, end_time, work_place });
        }

        return acc;
    }, {});

    //console.log(doctorId);
    //console.log(data);
    //console.log(transformedData);
    
    // заполняем календарь
    updateCalendarWithDates(Object.keys(transformedData));
    
    const dateDiv = document.querySelector('#date_select');
    if (!dateDiv) {
        console.error('Date select div not found');
        return;
    }
    dateDiv.innerHTML = '';
    
    Object.keys(transformedData).forEach(date => {
        const dateBtn = document.createElement('button');
        dateBtn.classList.add('time');
        dateBtn.textContent = date;
        dateBtn.id = 'dateBtn_' + date;
        dateBtn.style.display = 'none';
        dateDiv.appendChild(dateBtn);

        dateBtn.addEventListener('click', function () {
            deselectDateBtns();
            dateBtn.classList.add('selected');
            const times = getTimes(doctorId, serviceCode, date, transformedData , data);
            makeTimesDiv(times, date, data);
        });
    });

}

function deselectDateBtns() {
    const dateBtns = document.querySelectorAll('#date_select .time');
    dateBtns.forEach(dateBtn => {
        dateBtn.classList.remove('selected');
    });
}

function makeTimesDiv(times, data) {
    const timeDiv = document.querySelector('#time_select');
    timeDiv.innerHTML = '';
    times.forEach(time => {
        const timeBtn = document.createElement('button');
        timeBtn.classList.add('time');
        timeBtn.textContent = time.time;
        timeDiv.appendChild(timeBtn);

        timeBtn.addEventListener('click', function () {
            sendData(time, data);
        });
    });
}

function generateTimeSlots(start_time, end_time, time) {
    // Преобразуем строковые значения времени в минуты
    function timeToMinutes(timeStr) {
        const [hours, minutes, seconds] = timeStr.split(':').map(Number);
        return (hours * 60) + minutes + (seconds / 60);
    }

    // Преобразуем количество минут обратно в строку в формате 'чч:мм'
    function minutesToTime(minutes) {
        const hours = Math.floor(minutes / 60);
        const mins = Math.round(minutes % 60);
        return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}`;
    }

    const startMinutes = timeToMinutes(start_time);
    const endMinutes = timeToMinutes(end_time);
    const timeMinutes = timeToMinutes(time);  // Время, которое добавляется

    const result = [];
    let currentTime = startMinutes;

    // Генерация промежуточных временных слотов
    while (currentTime < endMinutes) {
        result.push(minutesToTime(currentTime));
        currentTime += timeMinutes;  // Увеличиваем текущее время на `time`
    }

    return result;
}

function sendData(time, date, data) {
    // Получаем элемент карточки, в которой произошел клик
    const card = event.target.closest('.doctor-appointment-card');
    const serviceSelect = card.querySelector('.service-select');
        
    if(serviceSelect.selectedIndex == 0) {
        alert('Выберите услугу!');
        return;
    }
    
    const birdDate = document.querySelector('#birth_date').value;
    const doctorId = card.dataset.doctorId;
    const serviceCode = serviceSelect.value;
    const serviceName = serviceSelect.options[serviceSelect.selectedIndex].textContent;

        // Находим цену услуги
    let servicePrice = '';
    if (data[doctorId]) {
        data[doctorId].forEach(schedule => {
            schedule.services.forEach(service => {
                if (service.code === serviceCode) {
                    servicePrice = service.price;
                }
            });
        });
    }
    
    const dataToSend = {
        birthDate: birdDate,
        doctorId: doctorId,
        serviceCode: serviceCode,
        serviceName: serviceName,
        servicePrice: servicePrice,
        work_place: time.work_place,
        date: date,
        time: time.time
    };
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/appointment-form';

    for (const key in dataToSend) {
        if (dataToSend.hasOwnProperty(key)) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = dataToSend[key];
            form.appendChild(input);
        }
    }

    document.body.appendChild(form);
    form.submit();
}

function initializeCalendar() {
    
    jQuery('#datepicker').datepicker({
        regional: 'ru',
        dateFormat: 'dd.mm.yy', // Формат даты
        beforeShowDay: function () {
            return [false, '', 'Недоступно']; // Все даты недоступны
        },
        onSelect: function (dateText) {
            const dateInput = jQuery('#appointment_date');
            dateInput.val(dateText); // Сохраняем выбранную дату
            calendarState.currentDay = Number(dateText.split('.', 1)[0]);
            let button = document.getElementById('dateBtn_' + dateText);
            if (button) {
                button.click();
            }

        },
        onChangeMonthYear : function (year, month, inst) {
            if (isCalendaReseting) return; 
            
            calendarState.currentMonth = month;
            calendarState.currentYear = year;

            const positionSelect = document.getElementById('position_select');
            const changeEvent = new Event('change', { bubbles: true, cancelable: true });
            positionSelect.dispatchEvent(changeEvent);
        },

    });
}

function get_current_calendar_date() {
    if (!calendarState.currentMonth || !calendarState.currentYear || !calendarState.currentDay) {
        const date = new Date();
        calendarState.currentMonth = date.getMonth() + 1;
        calendarState.currentYear = date.getFullYear();
        calendarState.currentDay = 1;
    }

    const year = calendarState.currentYear.toString();
    const month = calendarState.currentMonth.toString().padStart(2, '0'); // Добавляем ведущий ноль для месяца
    const day = calendarState.currentDay.toString().padStart(2, '0'); // Добавляем ведущий ноль для дня
    return `${year}${month}${day}`;
}

// получить текущую дату в формате "ггггммдд"
function get_today_date() {
    const date = new Date();
    const year = date.getFullYear().toString();
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Добавляем ведущий ноль для месяца
    const day = date.getDate().toString().padStart(2, '0'); // Добавляем ведущий ноль для дня
    return `${year}${month}${day}`;
}

// наполняем календарь датами
function updateCalendarWithDates(availableDates) {

    availableDates.sort(function(a, b) {
        // Преобразуем строки в объекты Date
        const dateA = new Date(a.split('.').reverse().join('-'));
        const dateB = new Date(b.split('.').reverse().join('-'));
        
        // Сравнение дат
        return dateA - dateB;
    });

    const $ = jQuery;
    
    const savedMonthAndYear = new Date(calendarState.currentYear, calendarState.currentMonth - 1, calendarState.currentDay);
    
    // Получаем текущие настройки календаря
    const options = $('#datepicker').datepicker('option');
    
    // Обновляем функцию beforeShowDay, чтобы учитывать доступные даты
    options.beforeShowDay = function (date) {
        const dateString = $.datepicker.formatDate('dd.mm.yy', date); // Убедитесь, что формат даты совпадает
        // Проверяем, доступна ли эта дата в массиве availableDates
        const isAvailable = availableDates.includes(dateString);
        return [isAvailable, '', isAvailable ? 'Доступно' : 'Недоступно'];
    };

    // Применяем изменения и перерисовываем календарь
    $('#datepicker').datepicker('option', options); // Обновляем опции
    $('#datepicker').datepicker('refresh'); // Перерисовываем календарь
    $('#datepicker').datepicker('setDate', savedMonthAndYear); // Устанавливаем нужный месяц и год
}

function resetUI() { 
    // Очищаем контейнер с карточками врачей
    const appointmentContainer = document.querySelector('#appointment_container');
    const existingCards = appointmentContainer.querySelector('.doctors-cards');
    if (existingCards) {
        existingCards.remove();
    }
}

function resetServiceSelect() {
    const serviceSelect = document.querySelector('.appointment select[name="service"]');
    serviceSelect.disabled = true;
    serviceSelect.innerHTML = '';
    const serviceOption = document.createElement('option');
    serviceOption.disabled = true; // Делаем эту опцию недоступной для выбора
    serviceOption.selected = true;
    serviceOption.textContent = 'Выберите услугу'; // Текст, который будет отображаться
    serviceSelect.appendChild(serviceOption);
}

function resetDoctorSelect() {
    const serviceSelect = document.getElementById('doctor_select');
    serviceSelect.disabled = true;
    serviceSelect.innerHTML = '';
    const option = document.createElement('option');
    option.disabled = true; // Делаем эту опцию недоступной для выбора
    option.selected = true;
    option.textContent = 'Выберите врача'; // Текст, который будет отображаться
    serviceSelect.appendChild(option);
}


function resetCalendar() {
    isCalendaReseting = true;
    const $ = jQuery;
    const savedMonthAndYear = new Date(calendarState.currentYear, calendarState.currentMonth - 1, calendarState.currentDay);
    
    // Получаем текущие настройки календаря
    const options = $('#datepicker').datepicker('option');
    
    // Обновляем функцию beforeShowDay, чтобы учитывать доступные даты
    options.beforeShowDay = function () {
        return [false, '', 'Недоступно']; 
    };

    // Применяем изменения и перерисовываем календарь
    $('#datepicker').datepicker('option', options); // Обновляем опции
    $('#datepicker').datepicker('refresh'); // Перерисовываем календарь
    $('#datepicker').datepicker('setDate', savedMonthAndYear); // Устанавливаем нужный месяц и год
    isCalendaReseting = false;
}

(function ($) {
    $(document).ready(function () {
        // Устанавливаем русский язык для календаря
        $.datepicker.setDefaults($.datepicker.regional['ru']);
        initializeCalendar();

    });
})(jQuery);

function searchFormHandler() {
    const searchBox = document.getElementById("searchBox");
    const searchInput = document.getElementById("searchInput");
    const searchButton = document.getElementById("searchSubmit");
    const toggleButton = document.getElementById("searchToggle");
    const logoDiv = document.querySelector(".header-top__logos");
    const formDiv = document.querySelector(".header-top__search_wrapper");

    toggleButton.addEventListener("click", () => {
        searchBox.classList.add("expanded");
        toggleButton.classList.add("hide");
//        toggleButton.remove();
        searchButton.classList.remove("hide");
        logoDiv.classList.add("search-expanded");
        formDiv.classList.add("search-expanded");
        searchInput.focus();
    });

    searchInput.addEventListener("blur", () => {
        searchBox.classList.remove("expanded");
        toggleButton.classList.remove("hide");
        searchButton.classList.add("hide");
        logoDiv.classList.remove("search-expanded");
        formDiv.classList.remove("search-expanded");
    });
}
function getDoctorInfo(doctorId) {
    return fetch(ajax_object.ajax_url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'get_doctor_info',
            doctor_id: doctorId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            return data.data;
        }
        throw new Error(data.data);
    });
}

// Создание карточки врача
function createDoctorCard(doctor) {
    const card = document.createElement('div');
    card.className = 'doctor-appointment-card';
    card.dataset.doctorId = doctor.doctor_id;
    // Добавляем атрибут с начальным значением "false"
    card.dataset.isChildDoctor = 'false';
    
    // Обратите внимание на правильную структуру HTML
    card.innerHTML = `
        <div class="doctor-appointment-card__content">
            <div class="team__card">
                <div class="card-foto">
                    <img src="/wp-content/themes/polidemika/assets/images/no_photo.jpg">
                </div>
                <div class="card-info">
                    <div class="card-info__text">
                        <div class="card-info__fio">Загрузка...</div>
						<div class="card-info__rating">
                        	<div class="stars"></div>
                        	<div class="points">0.0</div>
                    	</div>
                    </div>
                    <div class="card-info__position">Загрузка...</div>
                </div>
            </div>
            <div class="doctor-calendar">
                <div class="datepicker"></div>
            </div>
        </div>
        <div class="doctor-appointment-card__footer">
            <select name="service" class="service-select" disabled>
                <option disabled selected>Выберите услугу</option>
            </select>
            <div class="time-select"></div>
        </div>
    `;
    
    return card;
}

// Обновление информации в карточке врача
function updateDoctorCard(card, doctorInfo, data) {
    const photoImg = card.querySelector('.card-foto img');
    const nameDiv = card.querySelector('.card-info__fio');
    const positionDiv = card.querySelector('.card-info__position');
    const ratingDiv = card.querySelector('.points');
    
    photoImg.src = doctorInfo.photo;
    nameDiv.textContent = doctorInfo.fio;
    positionDiv.textContent = doctorInfo.position?.name || '';
    ratingDiv.textContent = doctorInfo.rating;
    
    if (doctorInfo.position) {
        card.dataset.term = doctorInfo.position.term_id;
    }
}

// Настройка селекта услуг
function setupServiceSelect(card, doctorId, data) {
    const serviceSelect = card.querySelector('.service-select');
    const services = get_services(doctorId, data);
    
    serviceSelect.disabled = false;
    serviceSelect.innerHTML = ''; // Очищаем существующие опции
    
    // Добавляем опцию по умолчанию
    const defaultOption = document.createElement('option');
    defaultOption.disabled = true;
    defaultOption.selected = true;
    defaultOption.textContent = 'Выберите услугу';
    serviceSelect.appendChild(defaultOption);
    
    // Сначала добавляем услуги где is_first === true
    services.forEach(service => {
        if (service.is_first === true) {
            const option = document.createElement('option');
            option.value = service.code;
            option.textContent = service.name;
            serviceSelect.appendChild(option);
        }
    });
    
    // Затем добавляем остальные услуги
    services.forEach(service => {
        if (service.is_first !== true) {
            const option = document.createElement('option');
            option.value = service.code;
            option.textContent = service.name;
            serviceSelect.appendChild(option);
        }
    });

    // Проверяем есть ли сохраненная услуга в URL
    const hashService = getHashKey('s');
    if (hashService) {
        const option = serviceSelect.querySelector(`option[value="${hashService}"]`);
        if (option) {
            serviceSelect.value = hashService;
            serviceSelect.style.color = 'black';
        }
    }

    serviceSelect.addEventListener('change', function() {
        const serviceCode = this.value;
        this.style.color = (serviceCode ? 'black' : 'gray');
        setHashKey('s', serviceCode);
        dateSelect(doctorId, serviceCode, data);
        $('.ui-datepicker-current-day').click();
    });
}

// Настройка календаря для карточки врача
function setupDoctorCalendar(card, doctorId, data) {
    const calendarDiv = card.querySelector('.doctor-calendar .datepicker');
    if (!calendarDiv) {
        console.error('Calendar div not found');
        return;
    }

    const dates = getDates(doctorId, data);
    
    // Важно: используем jQuery для поиска элемента
    $(calendarDiv).datepicker({
        regional: 'ru',
        dateFormat: 'dd.mm.yy',
        beforeShowDay: function(date) {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (date < today) {
                return [false, '', 'Недоступно'];
            }
            

            const dateString = $.datepicker.formatDate('dd.mm.yy', date);
            const isAvailable = dates.some(d => d.date === dateString);
            return [isAvailable, '', isAvailable ? 'Доступно' : 'Недоступно'];
        },
        onSelect: function(dateText) {
            // Находим все временные слоты для выбранной даты
            const selectedDates = dates.filter(d => d.date === dateText);
            
            if (selectedDates && selectedDates.length > 0) {
                // Передаем все найденные слоты
                showAvailableTime(card, selectedDates, doctorId, data);
            }
        }
    });

    // Принудительно обновляем календарь
    $(calendarDiv).datepicker('refresh');
}

function showAvailableTime(card, dateInfoArray, doctorId, data) {
    const timeSelect = card.querySelector('.time-select');
    timeSelect.innerHTML = '';

    const now = new Date();
    const localOffset = now.getTimezoneOffset();
    // Екатеринбург UTC+5 (300 минут)
    const ekbOffset = 5 * 60;
    const currentTime = new Date(now.getTime() + (localOffset + ekbOffset) * 60 * 1000);

    // Получаем serviceCode из выбранной услуги
    const serviceSelect = card.querySelector('.service-select');
    const serviceCode = parseInt(serviceSelect.value);
    
    // Определяем время приема так же, как в getTimesOneRow
    const availableDates = data[doctorId];
    let time = "0:15:00";

    availableDates.forEach(availableDate => {
        if (availableDate.date === dateInfoArray[0].date) {
            if (!serviceCode || serviceCode === 0) {
                // Ищем первичный прием
                const primaryService = availableDate.services.find(service => service.is_first === true);
                time = (primaryService && primaryService.time) ? primaryService.time : time;
            } else {
                availableDate.services.forEach(service => {
                    if (service.code === serviceCode) {
                        time = service.time ? service.time : time;
                    }
                });
            }
        }
    });

    // Собираем все временные слоты для всех периодов выбранной даты
    const allTimeSlots = [];
    
    const today = currentTime.toLocaleDateString('ru-RU');
    const isToday = dateInfoArray[0].date === today;
    
    dateInfoArray.forEach(dateInfo => {
        const timeSlots = generateTimeSlots(dateInfo.start_time, dateInfo.end_time, time);
        timeSlots.forEach(timeSlot => {
            if (isToday) {
                const [hours, minutes] = timeSlot.split(':');
                const slotTime = new Date(currentTime);
                slotTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
                
                // Пропускаем слоты, время которых уже прошло
                if (slotTime <= currentTime) {
                    return;
                }
            }

            allTimeSlots.push({
                time: timeSlot,
                work_place: dateInfo.work_place,
                date: dateInfo.date
            });
        });
    });

    // Сортируем все слоты по времени
    allTimeSlots.sort((a, b) => a.time.localeCompare(b.time));
    
    // Создаем кнопки для каждого временного слота
    allTimeSlots.forEach(slot => {
        const timeBtn = document.createElement('button');
        timeBtn.className = 'time-slot';
        timeBtn.textContent = slot.time;
        timeBtn.addEventListener('click', () => {
            const serviceSelect = card.querySelector('.service-select');
            if (serviceSelect.value) {
                sendData({ time: slot.time, work_place: slot.work_place }, slot.date, data);
            } else {
                alert('Выберите услугу!');
            }
        });
        timeSelect.appendChild(timeBtn);
    });
}

// Основная функция рендеринга карточек
function renderDoctorCards(data) {
    if (!data || data === null) {
        return;
    }
    
    const doctors = get_doctors(data);
    const appointmentContainer = document.querySelector('#appointment_container');
    
    // Очищаем предыдущие карточки
    const existingCards = appointmentContainer.querySelector('.doctors-cards');
    if (existingCards) {
        existingCards.remove();
    }
    
    // Создаем контейнер для карточек
    const cardsContainer = document.createElement('div');
    cardsContainer.className = 'doctors-cards';
    
    doctors.forEach(doctor => {
        const card = createDoctorCard(doctor);
        cardsContainer.appendChild(card);
        
        // Загружаем и обновляем информацию о враче
        getDoctorInfo(doctor.doctor_id)
            .then(doctorInfo => {
                updateDoctorCard(card, doctorInfo, data);
                
                // Если клиент ребенок и врач не детский, скрываем карточку
                if (isChildClient && !doctorInfo.is_child_doctor) {
                    card.style.display = 'none';
                }
                
                setupDoctorCalendar(card, doctor.doctor_id, data); // Сначала инициализируем календарь
                setupServiceSelect(card, doctor.doctor_id, data);  // Потом настраиваем селект услуг
            })
            .catch(error => {
                console.error('Ошибка загрузки информации о враче:', error);
            });
    });
    
    appointmentContainer.appendChild(cardsContainer);

    // Проверяем наличие doctor_id в хэше и скроллим к соответствующей карточке
    const hashDoctor = getHashKey('d');
    if (hashDoctor) {
        // Даем время на рендеринг карточек
        setTimeout(() => {
            const targetCard = cardsContainer.querySelector(`.doctor-appointment-card[data-doctor-id="${hashDoctor}"]`);
            if (targetCard) {
                targetCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }, 100);
    }
}

















