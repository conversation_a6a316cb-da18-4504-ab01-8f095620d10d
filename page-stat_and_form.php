<?php
/**
 * Template Name: Statia and forma
 *
 * @package polidemika
 */

get_header();
?>
<style>
	.team { justify-content: start; }
	.team__card .card-info__position { overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
	
	.diw_h5 { font-weight: 500; font-size: 14px; line-height: 17.07px; letter-spacing: 2px; color: #AEB3C0; display: flex; justify-content: space-between; margin-bottom: 20px; margin-top: 20px; }
	.diw_oio_wrap { margin-bottom: 10px; }
	.diw_oio_row { margin-bottom: 20px; }
	.diw_oio_bubble { color: #fff; background: rgba(116, 172, 251, 1); padding: 5px 34px; margin-right: 20px; border-radius: 12px; }
	.diw_table { background: #fff; box-shadow: 0px 20px 30px 0px rgba(0, 0, 0, 0.05); padding: 16px 0px; margin-bottom: 80px; border-radius: 12px; }
	.diw_table_row { display: grid; grid-template-columns: 3fr 1fr; gap: 16px; padding: 10px 34px; }
	.diw_table_row:nth-child(2n) { background: rgba(116, 172, 251, 0.05); }
	.diwtr_price { text-align: right; }

	.bigcat_name { margin-bottom: 40px; font-size: 32px; font-weight: 800; line-height: 39.01px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; }
	.post-thumbnail { margin-bottom: 40px; }
	.title_cat { color: rgba(174, 179, 192, 1); font-size: 16px; font-weight: 600; line-height: 19.5px; letter-spacing: 2px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; margin-bottom: 20px; }
	.entry-header { background: transparent; } 
	.entry-title { margin: 0 0 14px; font-size: 24px; font-weight: 800; line-height: 29.26px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; }
	.entry-content { margin-bottom: 60px; }
</style>
      
<?php
/**
 * Специалисты на главной
 */
?>

<div class="container mt20" id="specialists"> 
    <div class="block-f">
        <div class="block-title"><h1 class="entry-title"><?php the_title(); ?></h1></div>
<!--form>
  <div class="__select" data-state="">
    <div id="__select__title_id" class="__select__title" data-default="Выберите направление">Выберите направление</div>
    <div class="__select__content">
<?php
$categories = get_categories( [
	'taxonomy' => 'position',
	'orderby' => 'name',
	'order' => 'ASC'
] );
$i == 0;
foreach( $categories as $category ){ 
	$i++; ?>
	<input id="singleSelect<?php echo $i; ?>" class="__select__input" type="radio" name="singleSelect" />
	<label for="singleSelect<?php echo $i; ?>" class="__select__label" data-catid="<?php echo $category->term_id; ?>"><?php echo $category->name; ?></label>
<?php } ?>
    </div>
  </div>
</form-->		
    </div>

<article>
	
	<?php if(get_field('kartinka_stati')) { ?>
<div class="post-thumbnail">
				<img width="1180" height="488" src="<?php the_field('kartinka_stati') ?>" class="attachment-post-thumbnail size-post-thumbnail wp-post-image" alt="" decoding="async">
</div>
	<?php } ?>
	
	<header class="entry-header">

	</header><!-- .entry-header -->

	<div class="entry-content">
<?php the_content(); ?>
	</div><!-- .entry-content -->

</article>

	<div class="conts_form">
		<div class="cf_conts">
			<div class="cfc_wrap">
				<?php the_field('sami_kontakty', 887); ?>
				Наши соцсети<br>
				<div class="cf_soc_seti">
					<a href="<?php the_field('ssylka_vk', 887); ?>"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/vk_w.svg"></a>
					<a href="<?php the_field('ssylka_tg', 887); ?>"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/tg_w.svg"></a>
					<a href="<?php the_field('ssylka_yut', 887); ?>"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/yt_w.svg"></a>
				</div>
			</div>
		</div>
		<div class="cf_form">
			<?php echo do_shortcode('[contact-form-7 id="bf9b26f" title="Контактная форма 1"]'); ?>
		</div>
	</div>
	
	
</div>

					


    </div>
<?php
get_footer();