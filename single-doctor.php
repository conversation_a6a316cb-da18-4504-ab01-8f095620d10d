<?php
/**
 * The template for displaying all single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package polidemika
 */
//var_dump(get_the_ID());
get_header();
?>
<style>
	.diw_top_info { display: flex; margin: 30px 0 50px; }
	.diw_photo { width: 30%; aspect-ratio: 1 / 1; border-radius: 12px; overflow: hidden; margin-right: 3%; }
	.diw_photo img { width: 100%; height: 100%; object-fit: cover; }
	.diwi_right { display: flex; flex-direction: column; justify-content: space-between; }
	.diwir_title { font-size: 32px; font-weight: 800; line-height: 39.01px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; margin-bottom: 30px; }
	.diwir_info {  }
	.one_of_diwiri { display: flex; justify-content: space-between; margin-bottom: 10px; }
	.ood_left, .ood_right { width: 50%; }
	.ood_left { font-size: 14px; font-weight: 500; line-height: 17.07px; letter-spacing: 2px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; color: rgba(174, 179, 192, 1); text-transform: uppercase; }
	.ood_right { display: flex; font-size: 18px; font-weight: 500; line-height: 21.94px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; }
	.ood_stars { background-image: url(<?php echo get_template_directory_uri(); ?>/assets/images/stars_5.png); background-position: left center; background-repeat: no-repeat; width: 78px; height: 18px; margin-right: 8px; }
	.ood_right span { font-size: 16px; color: rgba(174, 179, 192, 1); }
	.diwiri_bttns { display: flex; }
	.bttn_left { margin-right: 20px; }
	.bttn_left button { border: 1px solid #AEB3C0; border-radius: 12px; padding: 6px 20px; font-size: 14px; background: linear-gradient(207.24deg, #74ACFB -7.82%, #0F61D9 104.72%); color: #fff; height: 55px; }
	.bttn_left button img { margin-right: 10px; }
	.bttn_right button { border: 1px solid #011424; height: 55px; background-color: transparent; border-radius: 12px; padding: 6px 20px; cursor: pointer; color: #011424; }
	.bttn_right button img { margin-right: 10px; }
		
	.diw_tabs { width: 100%; }
	.diw_tabs .diw_info { width: 100%; padding: 40px; position: relative; }
	.diw_tabs .diw_header { width: fit-content; margin: 0 auto; background: #fff; display: flex; justify-content: center; padding: 3px; border-radius: 12px; margin-bottom: 50px; }
	.diw_tabs .diw_header-tab { font-size: 16px; font-weight: 500; line-height: 20px; text-align: center; text-underline-position: from-font; text-decoration-skip-ink: none; padding: 15px 34px; border-radius: 10px; cursor: pointer; margin-right: 20px; }
	.diw_tabs .diw_header-tab:last-child { margin-right: 0; }
	.diw_tabs .diw_header-tab:hover, .diw_tabs .diw_header-tab.act { background: rgba(116, 172, 251, 1); color: #fff; }
	.diw_tabs .diw_tabcontent { width: calc(100% - 80px); display: flex; transition: 0.3s; } 
	.diw_tabs .diw_tabcontent .diw_description { font-weight: 400; width: 100%; }
	.diw_tabs .diw_info .hide { opacity: 0; pointer-events: none; position: absolute; top: 146px; transition: none; }
	.diw_tabs .diw_info .show { opacity: 1; pointer-events: auto; width: 100%; }
	.diw_h2 { font-size: 32px; font-weight: 800; line-height: 39.01px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; margin-bottom: 20px; }
	
	.diw_h3 { font-size: 24px; font-weight: 600; line-height: 29.26px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; margin-bottom: 20px; }
	.diw_h5 { font-weight: 500; font-size: 14px; line-height: 17.07px; letter-spacing: 2px; color: #AEB3C0; display: flex; justify-content: space-between; margin-bottom: 20px; }
	.diw_oio_wrap { margin-bottom: 10px; }
	.diw_oio_row { margin-bottom: 20px; }
	.diw_oio_bubble { color: #fff; background: rgba(116, 172, 251, 1); padding: 5px 34px; margin-right: 20px; border-radius: 12px; }
	.diw_table { background: #fff; box-shadow: 0px 20px 30px 0px rgba(0, 0, 0, 0.05); padding: 16px 0px; margin-bottom: 80px; border-radius: 12px; }
	.diw_table_row { display: grid; grid-template-columns: 3fr 1fr; gap: 16px; padding: 10px 34px; }
	.diw_table_row:nth-child(2n) { background: rgba(116, 172, 251, 0.05); }
	.diwtr_price { text-align: right; white-space: nowrap; }
	
	.diw_h4 { color: rgba(174, 179, 192, 1); margin: 90px 0 16px; font-size: 16px; font-weight: 600; line-height: 19.5px; letter-spacing: 2px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; }
	.diw_serts { display: grid; gap: 20px; grid-template-columns: 1fr 1fr 1fr; }
	.diw_serts img { border-radius: 12px; }
</style>
	<main id="primary" class="site-main">
		<?php
		while ( have_posts() ) :
			the_post(); ?>
<div class="doc_in_wrap">
	<div class="diw_top_info">
		<div class="diw_photo">
			<!--img src="<?php //the_field('photo'); ?>" /-->
			<img src="<?php echo get_doctor_photo( get_the_ID() ); ?>" />
		</div>
		<div class="diwi_right">
			<div class="diwir_title">
				<?php the_field('fio'); ?>
			</div>
			<div class="diwir_info">
				<div class="one_of_diwiri"><div class="ood_left">Специализация</div><div class="ood_right"><?php the_field('speczializacziya'); ?></div></div>
				<div class="one_of_diwiri"><div class="ood_left">Должность</div><div class="ood_right">
					<?php
                            $position = get_first_doctor_position( get_the_ID() );
                            if ( $position ) {
                                echo '<a href="' . get_term_link( $position ) . '">' . $position->name . '</a>';
                            }
                        ?>
					</div></div>
				<div class="one_of_diwiri"><div class="ood_left">Стаж</div><div class="ood_right"><?php  
if (get_field('stazh')) {
$originalDate = get_field('stazh');
$dateObj = DateTime::createFromFormat('d/m/Y', $originalDate);
$newDate = $dateObj->format('Y-m-d');
					
$startDate = new DateTime($newDate);
$currentDate = new DateTime();

$interval = $currentDate->diff($startDate);

$years = $interval->y;
$months = $interval->m;

echo plural($years, ['год', 'года', 'лет']) . " и " . 
     plural($months, ['месяц', 'месяца', 'месяцев']);
}
					?></div></div>
				<div class="one_of_diwiri"><div class="ood_left">Рейтинг</div><div class="ood_right"><div class="ood_stars"></div><?php the_field('score'); ?><span>/5</span></div></div>
				<div class="one_of_diwiri"><div class="ood_left">Стоимость приема</div><div class="ood_right"><?php the_field('stoimost_priema'); ?></div></div>
			</div>
			<div class="diwiri_bttns">
                <?php
                    $hashItems = array();
                    // Код направления
                    if($doctor_position = term_description($position)) {
                        $doctor_position = strip_tags($doctor_position);
                        $hashItems[] = "p:{$doctor_position}";
                    }
                    // Код врача
                    if($doctor_code = get_post_meta(get_the_ID(), 'code', true)) {
                        $doctor_code = str_pad($doctor_code, 11, '0', STR_PAD_LEFT);
                        $hashItems[] = "d:{$doctor_code}";
                    }
                ?>
              	<a class="bttn_left" href="https://polymedica86.com/appointment/#<?= implode('_', $hashItems); ?>">
                	<button><img src="https://polymedica86.com/wp-content/themes/polidemika/assets/images/appointment.svg" alt="">Записаться на прием</button>
              	</a>
            	<a class="bttn_right" href="https://polymedica86.com/appointment/#<?= implode('_', $hashItems); ?>">
                	<button><img src="https://polymedica86.com/wp-content/themes/polidemika/assets/images/generic_avatar.svg" alt="">Заказать звонок</button>
            	</a>
			</div>
		</div>
	</div>
	<div class="diw_tabs">
        <div class="diw_container">
            <div class="diw_info" >
                <div class="diw_header">
                    <div class="diw_header-tab act">О враче</div>
                    <div class="diw_header-tab">Опыт и образование</div>
                    <div class="diw_header-tab">Услуги и цены</div>
                    <div class="diw_header-tab" onclick="slicks();">Отзывы</div>
                </div>
                <div class="diw_tabcontent">
                    <div class="diw_description">
						<div class="diw_h2">О враче</div>
                        <?php the_field('vkladka_o_vrache'); ?>
                    </div>
                </div>
                <div class="diw_tabcontent">
                    <div class="diw_description">
						<div class="diw_h2">Опыт и образование</div>
<?php
if( have_rows('opyt_i_obrazovanie') ):
    while ( have_rows('opyt_i_obrazovanie') ) : the_row(); ?>
		<div class="diw_h3"><?php the_sub_field('zagolovok'); ?></div>
		<?php if( have_rows('strochka_infy') ): ?>
			<div class="diw_oio_wrap">
    		<?php while ( have_rows('strochka_infy') ) : the_row(); ?>
				<div class="diw_oio_row">
				<?php if( get_sub_field('data') ): ?>
        			<span class="diw_oio_bubble"><?php the_sub_field('data'); ?></span>
				<?php else :
				endif;
				the_sub_field('opisanie'); ?>
				</div>
    		<?php endwhile; ?>
			</div>
		<?php else :
		endif;
    endwhile;
else :
endif; ?>
	<div class="diw_h4">Гарантии качества</div>
	<div class="diw_h2">Сертификаты</div>
<?php 
$images = get_field('sertifikaty');
$size = 'full'; // (thumbnail, medium, large, full или произвольный размер)
if( $images ): ?>
    <div class="diw_serts">
        <?php foreach( $images as $image ): ?>
            	<?php echo wp_get_attachment_image( $image['ID'], $size ); ?>
        <?php endforeach; ?>
    </div>
<?php endif; ?>
                    </div>
                </div>
                <div class="diw_tabcontent">
                    <div class="diw_description">
						<div class="diw_h2">Услуги и цены</div>
						<div class="diw_h5"><span>Название услуги</span><span>Стоимость, рублей</span></div>
                        <div class="diw_table">
                        <?php
                        $doctor_code = get_post_meta( get_the_ID(), 'code', true );
                        $doctor_code = str_pad( $doctor_code, 11, '0', STR_PAD_LEFT );
                        $services    = get_doctor_services( $doctor_code );

                        if ( $services && is_array( $services ) ) {
                            foreach ( $services as $service ) {
                                echo '<div class="diw_table_row">';
                                echo '<div>' . esc_html( $service['name'] ) . '</div>';
                                echo '<div class="diwtr_price">' . number_format( (float) $service['price'], 0, ',', ' ' ) . '&nbsp;₽</div>';
                                echo '</div>';
                            }
                        } else {
                            echo '<div class="diw_table_row"><div>Нет доступных услуг</div><div class="diwtr_price">—</div></div>';
                        }
                        ?>
                        </div>
                    </div>
                </div>
                <div class="diw_tabcontent">
                   <div class="diw_description">
					   <div class="diw_h2">Отзывы</div>
<?php
/**
 * Отзывы на главной
 */

 $args = array(
    'post_type'      => 'reviews',
    'posts_per_page' => 6, 
);


$reviews= get_posts( $args );
?>
    <div class="testimonial">
        <?php foreach ( $reviews as $review ) : ?>
            <div class="testimonial__card">
                <div class="t-card__top">
                    <div class="t-card__person">
                    <div class="t-card__photo">
                        <?php $img_src = esc_url( get_field( 'review_foto', $review->ID ) ); ?>
                        <img src="<?php echo $img_src; ?>">
                    </div>
                    <div class="t-card__title">
                        <div class="t-card__name">
                            <?php echo get_field( 'review_name', $review->ID ); ?>
                        </div>
                        <div class="t-card__date">
                        <?php echo get_field( 'review_date', $review->ID ); ?>
                        </div>
                    </div>
                    </div>
                    <div class="t-card__rating"> 
                        <div class="t-card__logo">
                            <img src="<?php echo get_field( 'review_logo', $review->ID ); ?>">
                        </div>
                        <div class="t-card__points">
                            <?php echo get_field( 'review_score', $review->ID ); ?>
                        </div>
                    </div>
                </div>
                <div class="t-card__text"><?php echo get_field( 'review_text', $review->ID ); ?></div>
                </div>
        <?php endforeach; ?>
    <!--div class="testimonial-nav"> 
        <div class="testimonial-nav__gray"> </div>
        <div class="testimonial-nav__blue"></div>
        <div class="testimonial-nav__gray"> </div>
    </div-->

<?php wp_reset_postdata(); ?>
                    </div>
                </div>
              </div>
         </div>
<script>
	window.addEventListener('DOMContentLoaded', function() {
    'use strict';
    let tab = document.querySelectorAll('.diw_header-tab'),
        header = document.querySelector('.diw_header'),
        tabContent = document.querySelectorAll('.diw_tabcontent');
    function hideTabContent(a) {
        for (let i = a; i < tabContent.length; i++) {
            tabContent[i].classList.remove('show');
            tabContent[i].classList.add('hide');
			tab[i].classList.remove('act');
            tab[i].classList.add('dis');
        }
    }
    hideTabContent(1);
    function showTabContent(b) {
        if (tabContent[b].classList.contains('hide')) {
            tabContent[b].classList.remove('hide');
            tabContent[b].classList.add('show');
			tab[b].classList.remove('dis');
            tab[b].classList.add('act');
        }
    }
    header.addEventListener('click', function(event) {
        let target = event.target;
        if (target && target.classList.contains('diw_header-tab')) {
            for(let i = 0; i < tab.length; i++) {
                if (target == tab[i]) {
                    hideTabContent(0);
                    showTabContent(i);
                    break;
                }
            }
        }

    });
});
</script>	
</div>
	
<?php get_template_part( 'template-parts/faq' ); ?>
<?php endwhile; // End of the loop.	?>

	</main><!-- #main -->

<?php
//get_sidebar();
get_footer();