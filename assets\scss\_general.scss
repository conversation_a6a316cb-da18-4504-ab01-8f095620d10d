body {
    @extend %font_500;
    font-size: 14px;
    background-color: #F5F6FA;
}

a {
    color: $color_blue;
}

.container {
    max-width: 1180px;
    margin: 0 auto;
    padding: 0 5px;
}

.mt20 {
    margin-top: 20px;
}

.mt40 {
    margin-top: 40px;
}


.main-container {
    background-color: #F5F6FA;
}

.block-name {
    @extend %font_600;
    font-size: 16px;
    color: $color_grey;
    text-transform: uppercase;
}

.block-title {
    @extend %font_800;
    font-size: 32px;
}
.block-f {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.block-link {
    font-size: 16px;
    letter-spacing: 2px;
    text-transform: uppercase;
    color: #1062D9;

}

.center {
    text-align: center;
    display: flex;
    justify-content: center;
}

.submit_button {
    border: 1px solid #AEB3C0;
    border-radius: 12px;
    padding: 6px 10px;
    font-size: 14px;
    background: linear-gradient(207.24deg, #74ACFB -7.82%, #0F61D9 104.72%);

    color: #fff;
    height: 55px;
    img {
        margin-right: 10px;
    }
}
/* Затемняющий фон */
#overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5); /* Затемнение */
    z-index: 999; /* Убедитесь, что фон на переднем плане */
    display: none;
}

/* Модальное окно */
#loadingModal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 20px 40px;
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
    z-index: 1000; /* Модальное окно должно быть сверху */
    display: none;
}

#loadingModal .modal-content {
    text-align: center;
}

/* Блюр фона */
body.blurred {
    filter: blur(5px); /* Блюрим фон */
    pointer-events: none; /* Отключаем взаимодействие с размытым фоном */
}

.hide {
    display: none;
}