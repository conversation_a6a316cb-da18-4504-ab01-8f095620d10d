.appointment {
    width: 900px;
    margin: 40px 0;
    padding: 60px 40px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0px 20px 30px 0px #0000000D;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    flex-wrap: wrap;

    &__block {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        gap: 10px;
        width: 380px;

        form {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: flex-start;
            gap: 20px;
        }

        input {
            border-radius: 12px;
            border: none;
            background-color: #F3F4F7;
            padding: 16px 16px 16px 16px;
            width: 380px;

            &:focus {
                outline: none;                
            }
        }

        select {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-color: #F3F4F7;
            background-image: url('../images/arrow-down-select.svg');
            border-radius: 12px;
            border: none;
            padding: 16px 16px 16px 16px;
            width: 380px;
            background-repeat: no-repeat;
            background-position: right 1rem center;
            background-size: 0.667em 0.667em;

            &:focus {
                outline: none;
            }
          }
    }

    &__times {
        margin-top: 20px;
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        justify-content: flex-start;
        gap: 10px;
        flex-wrap: wrap;
    }

    &__info {
        img {
            margin-right: 10px;
        }
    }

    .time {

        border: none;
        background-color: #F3F4F7;
        border-radius: 12px;
        padding: 10px 30px 10px 10px;
        background-image: url(../images/check.svg);
        background-repeat: no-repeat;
        background-position: right 10px center;
        cursor: pointer;
        user-select: none; 

        &:focus {
            outline: none;
        }

        &.selected {
            background-color: #74ACFB;
            color: #fff;
            background-image: url(../images/check_white.svg);
        }
    }
}

// Добавляем новые стили для карточек докторов
.doctors-cards {
    margin-top: 20px;
    width: 100%;
}

.doctor-appointment-card {
    width: 900px;
    margin: 40px auto;
    padding: 60px 40px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0px 20px 30px 0px #0000000D;

    &__content {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        gap: 40px;

        .team__card {
            width: 45%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            padding: 20px;
            box-shadow: none;
        }

        .doctor-calendar {
            width: 45%;
            margin-top: 0;
        }
    }

    @media (max-width: 768px) {
        width: 100%;
        padding: 20px;
        margin: 20px auto;

        &__content {
            flex-direction: column;
            gap: 20px;

            .team__card,
            .doctor-calendar {
                width: 100%;
            }
        }
    }

    .team__card {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding: 20px;
        box-shadow: none;

        .card-foto {
            height: 260px;
            width: 100%;
            border-radius: 12px;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .card-info {
            margin-top: 30px;
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: space-between;

            &__text {
                flex: 1;
            }

            &__fio {
                max-width: 200px;
                @extend %font_600;
                font-size: 16px;
            }

            &__position {
                margin-top: 10px;
                @extend %font_500;
                font-size: 16px;
                color: $color_grey;
            }

            &__rating { 
                display: flex;
                flex-direction: row;
                align-items: center;

                .stars {
                    background-image: url(../images/stars_5.png);
                    background-repeat: no-repeat;
                    width: 78px;
                    height: 13px;
                    margin-right: 8px;
                }

                .points {
                    @extend %font_600;
                }
            }
        }
    }

    .doctor-calendar {
        width: 45%;

        @media (max-width: 768px) {
            width: 100%;
            display: flex;
            justify-content: center;

            .datepicker {
                margin: 0 auto;
            }
        }
    }

    // Нижняя часть - выбор услуги и времени
    &__footer {
        width: 100%;
        margin-top: 30px;

        .service-select {
            width: 100%;
            margin-bottom: 15px;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-color: #F3F4F7;
            background-image: url('../images/arrow-down-select.svg');
            border-radius: 12px;
            border: none;
            padding: 16px 16px 16px 16px;
            background-repeat: no-repeat;
            background-position: right 1rem center;
            background-size: 0.667em 0.667em;

            &:focus {
                outline: none;
            }
        }

        .time-select {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;

            .time-slot {
                border: none;
                background-color: #F3F4F7;
                border-radius: 12px;
                padding: 10px 30px 10px 10px;
                background-image: url(../images/check.svg);
                background-repeat: no-repeat;
                background-position: right 10px center;
                cursor: pointer;
                user-select: none; 

                &:focus {
                    outline: none;
                }

                &.selected {
                    background-color: #74ACFB;
                    color: #fff;
                    background-image: url(../images/check_white.svg);
                }
            }
        }
    }
}

.ui-datepicker {
    background-color: #F3F4F7 !important;
}

.ui-widget-header {
    background: none !important;
    border: none !important;
}

.ui-widget-header .ui-icon {
    background-image: url('../images/arrow-left.svg') !important;
}

.ui-icon-circle-triangle-e {
    background-position: center !important;
    transform: rotate(180deg) !important;
}
.ui-icon-circle-triangle-w { 
    background-position: center !important;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default, .ui-button, html .ui-button.ui-state-disabled:hover, html .ui-button.ui-state-disabled:active {
    border: none !important;
}
.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight {
    background: none !important;
}

.ui-state-default {
    width: 34px !important;
    height: 34px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.ui-state-default {
    background-color: #1976D21F !important;
    background-image: none !important;
    border-radius: 50%  !important;
}
.ui-state-default.ui-state-highlight {
    background-color: #deedfa !important;
    background-image: none !important;
    border-radius: 50% !important;
}

.ui-state-default.ui-state-active {
    background-color: #0F61D9 !important;
    color: #fff !important;
}

.ui-datepicker td {
    padding: 0 !important;
}
td.ui-datepicker-unselectable span.ui-state-default {
    background-color: #fff !important;
}

.ui-datepicker-calendar td {
    padding: 1px !important;
}

.ui-widget {
    font-family: $font_family !important;
}

.ui-widget-content {
    border: 0 !important;
    padding: 20px !important;
    width: 320px !important;
    user-select: none;
}
.custom-select {
    position: relative;
    width: 380px;
    
    @media (max-width: 768px) {
        width: 100%;
    }
    
    &__search {
        width: 100%;
        padding: 16px;
        border: none;
        border-radius: 12px;
        background-color: #F3F4F7;
        box-sizing: border-box;
        color: gray;
        cursor: pointer;
        background-image: url('../images/arrow-down-select.svg');
        background-repeat: no-repeat;
        background-position: right 1rem center;
        background-size: 0.667em 0.667em;
        
        &:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(16, 98, 217, 0.2);
        }
        
        &::placeholder {
            color: #999;
        }
    }
    
    &__options {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #fff;
        border: 1px solid #F3F4F7;
        border-top: none;
        border-radius: 0 0 12px 12px;
        max-height: 300px;
        overflow-y: auto;
        z-index: 1000;
        margin-top: 4px;
        
        .open & {
            display: block;
        }
    }
    
    &__option {
        padding: 12px 16px;
        cursor: pointer;
        
        &:hover {
            background-color: #F3F4F7;
        }
    }
}

@media screen and (max-width: 620px) {
    .doctor-appointment-card {
        .team__card {
            width: 100%;
        }
    }
}

.f-align-end {
    align-items: flex-end;
}