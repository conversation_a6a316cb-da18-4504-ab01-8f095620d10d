<?php
/**
 * Специалисты на главной
 */

$args = array(
	'post_type' => 'doctor',
	'include'   => '575,588,639',
	'posts_per_page' => 3,
//	'tax_query' => array(
//        array(
//            'taxonomy' => 'podrazdelenie', 
//            'field'    => 'name',         
//            'terms'    => 'на главную',
//        ),
//    ),
//	'orderby' => 'ID',
  	'order' => 'DESC',
);

$doctors = get_posts( $args );
//update_doctors_from_xml();
?>

<div class="container mt20" id="specialists"> 
    <div class="block-name">Наша комманда</div>
    <div class="block-f">
        <div class="block-title">Специалисты клиники</div>
        <div class="block-link"><a href="/speczialisty/">Показать всех</a></div>
    </div>
    <div class="team">
        <?php foreach ( $doctors as $doctor ) : ?>
            <div class="team__card">
                <div class="card-foto" style="position: relative;">
<?php if (get_field('stazh', $doctor->ID)) { ?>
					<div class="staje" style="position: absolute; top: 0; right: 13px; width: 71px; height: 78px; box-sizing: border-box; padding: 6px 0 10px; background: #fff; box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.05); display: flex; flex-direction: column; align-items: center; border-radius: 0 0 8px 8px;">
						<img style="width: 24px; height: 24px; margin-bottom: 8px;" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/staje.svg">
						<div style="text-align: center; line-height: 1.2">
						<span style="color: rgba(174, 179, 192, 1)">опыт</span><br>
<?php  
$originalDate = get_field('stazh', $doctor->ID);
$dateObj = DateTime::createFromFormat('d/m/Y', $originalDate);
$newDate = $dateObj->format('Y-m-d');
				
$startDate = new DateTime($newDate);
$currentDate = new DateTime();

$interval = $currentDate->diff($startDate);

$years = $interval->y;
$months = $interval->m;

echo plural($years, ['год', 'года', 'лет']);
?>
						</div>
					</div>
<?php } ?>
                    <img src="<?php echo get_doctor_photo( $doctor->ID ); ?>">
                </div>
                <div class="card-info"> 
                    <div class="card-info__text">
                    <div class="card-info__fio">
                        <?php echo get_doctor_fio( $doctor->ID ); ?>
                    </div>
                    <div class="card-info__rating">
                    <div class="stars"> </div>
                    <div class="points">4.8</div>
                    </div>
                    </div>
					<div class="card-info__position">
                        <?php
                            $position = get_first_doctor_position( $doctor->ID );
                            if ( $position ) {
                                echo '<a href="' . get_term_link( $position ) . '">' . $position->name . '</a>';
                            }
                        ?>
                        
                    </div>
                    
                </div>
                <div class="card-buttons"> 
                    <a href="<?php echo home_url(); ?>/appointment">
                        <button><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/appointment-blue.svg" alt="">Записаться на прием</button>
                    </a>
                    <a class="card-link" href="<?php echo $doctor->guid; ?>">Подробнее</a>
                </div>
            </div>
        <?php endforeach; ?>
        
    </div>
</div>
<? wp_reset_postdata(); ?>