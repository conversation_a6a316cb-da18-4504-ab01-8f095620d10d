header, footer {
    background-color: #fff;
}
.header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    padding: 10px 0;
    background-color: #fff;
    &__logos {
        display: flex;
        align-items: center;
        flex-direction: row;

        // прячем лога при поиске на мобилке
        &.search-expanded {
            @media screen and (max-width: 620px) {
                display: none;
            }
        }
        
        .logo {
            margin-right: 40px;
            img {
                max-height: 45px;
                @media screen and (max-width: 768px) {
                    max-height: 35px;
                }
            }
        }
        .low-vision {
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #F3F4F7;
            border-radius: 50%;
            width: 48px;
            height: 40px;
        }
    }
    &__info {
        display: flex;
        align-items: center;
        flex-direction: row;
        gap: 10px;
        min-width: 550px;
        @media screen and (max-width: 620px) {
            display: none;
        }

        .header-top__phone {
            img {
                margin-right: 6px;
            }
            a {
                font-size: 12px;
                color: $color_text;
            }
        }

        a.button, button {
            border: 1px solid #AEB3C0;
            border-radius: 12px;
            padding: 6px 10px;
            font-size: 14px;
            color: $color_text;

            &:hover {
                //color: #fff;
                //background: linear-gradient(207.24deg, #74ACFB -7.82%, #0F61D9 104.72%);
                box-shadow: 0px 4px 4px rgba(15, 97, 217, 0.25);
                text-decoration: none;
            }

            &:focus {
                outline: none;
            }

            &:active {
                background: linear-gradient(207.24deg, #74ACFB -7.82%, #0F61D9 104.72%);
                box-shadow: 0px 4px 4px rgba(15, 97, 217, 0.25);
            }
        }

        img {
            margin-right: 10px;
        }
    }
    &__menu {
        display: flex;
        align-items: center;
        ul{
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex-direction: row;
            gap: 30px;
            list-style: none;
            margin: 0;
            padding: 0;

            li {
                margin: 0;
                padding: 0;
            }

            a {
                font-size: 14px;
                color: $color_text;
            }

            a.current {
                color: #0F61D9;
            }
        }
    }
    &__appointment {
        align-self: flex-start;
        button {
            border: 1px solid #AEB3C0;
            border-radius: 12px;
            padding: 6px 10px;
            font-size: 14px;
            background: linear-gradient(207.24deg, #74ACFB -7.82%, #0F61D9 104.72%);

            color: #fff;
            height: 40px;
            img {
                margin-right: 10px;
            }
        }
    }

    &__search_wrapper {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 100%;
        max-width: 300px;

        @media screen and (max-width: 620px) {
            width: 40px;
            &.search-expanded {
                width: 100%;
                max-width: 100%;
            }
        }
    }

    .search-container {
        display: flex;
        align-items: center;
        border: 1px solid #ccc;
        border-radius: 10px;
        width: 32px;
        overflow: hidden;
        transition: width 0.3s ease-in-out;
        padding: 4px;
        margin-left: 10px;
    }
    .search-container form {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .search-container.expanded {
        width: 100%;
    }
    .search-input {
        display: none;
        border: none;
        outline: none;
        width: 0;
        opacity: 0;
        transition: width 0.3s ease-in-out, opacity 0.3s ease-in-out;
    }
    .search-container.expanded .search-input {
        width: auto;
        opacity: 1;
        display: block;
    }
    .search-icon {
        width: 22px;
        height: 22px;
        cursor: pointer;
        transition: transform 0.3s ease-in-out;
    }
    .search-container.expanded .search-icon {
        transform: translateX(0px);
    }
    .search-icon-btn {
        border: none;
        margin: 0;
        padding: 0;
    }
}