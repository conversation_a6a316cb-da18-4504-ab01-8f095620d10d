<?php
/**
/* Template Name: Contacts Page
 *
 * @package polidemika
 */

get_header();
?>
<style>
	.block-f { margin-bottom: 40px; }
	.wrap_offices { display: grid; gap: 24px; grid-template-columns: 1fr 1fr; margin-bottom: 24px; }
	.one_office { border-radius: 12px; overflow: hidden; background: #fff; }
	.one_office .oo_img { width: 100%; }
	.oo_padding { padding: 40px 30px 60px; }
	.oo_h2 { margin-bottom: 30px; font-size: 24px; font-weight: 800; line-height: 29.26px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; }
	.oo_h3 { color: rgba(174, 179, 192, 1); margin-bottom: 8px; font-size: 18px; font-weight: 500; line-height: 21.94px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; }
	.oo_p { margin-bottom: 20px; font-size: 18px; font-weight: 500; line-height: 24px; text-align: left; text-underline-position: from-font; text-decoration-skip-ink: none; }
	.bttn_right button { border: 1px solid #011424; height: 55px; background-color: transparent; border-radius: 12px; padding: 6px 45px; cursor: pointer; color: #011424; }
	.bttn_right button img { margin-right: 10px; }
</style>
<div class="container mt20" id="specialists"> 
    <div class="block-f">
        <div class="block-title">Контакты</div>
    </div>
<?php
if( have_rows('ofisy') ): ?>
	<div class="wrap_offices">
    <?php while ( have_rows('ofisy') ) : the_row(); ?>
		<div class="one_office">
			<img class="oo_img" src="<?php the_sub_field('kartinka'); ?>" />
			<div class="oo_padding">
				<div class="oo_h2"><?php the_sub_field('nazvanie'); ?></div>
				<div class="oo_h3">Адрес</div>
				<div class="oo_p"><?php the_sub_field('adres'); ?></div>
				<div class="oo_h3">Часы работы</div>
				<div class="oo_p"><?php the_sub_field('chasy_raboty'); ?></div>
				<div class="oo_h3">Парковка</div>
				<div class="oo_p"><?php the_sub_field('parkovka'); ?></div>
				<a class="bttn_right" href="<?php the_sub_field('ssylka_pod_knopkoj'); ?>">
                	<button><img src="<?php echo get_template_directory_uri(); ?>/assets/images/map.svg" alt="">Как добраться</button>
            	</a>
			</div>
		</div>
    <? endwhile; ?>
	</div>
<?php else :
endif; ?>	

	<div class="conts_form">
		<div class="cf_conts">
			<div class="cfc_wrap">
				<?php the_field('sami_kontakty'); ?>
				Наши соцсети<br>
				<div class="cf_soc_seti">
					<a href="<?php the_field('ssylka_vk'); ?>"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/vk_w.svg"></a>
					<a href="<?php the_field('ssylka_tg'); ?>"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/tg_w.svg"></a>
					<a href="<?php the_field('ssylka_yut'); ?>"><img src="<?php echo get_template_directory_uri(); ?>/assets/images/yt_w.svg"></a>
				</div>
			</div>
		</div>
		<div class="cf_form">
			<?php echo do_shortcode('[contact-form-7 id="bf9b26f" title="Контактная форма 1"]'); ?>
		</div>
	</div>
	
</div>

    </div>
<?php
get_footer();
