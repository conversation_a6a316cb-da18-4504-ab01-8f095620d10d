<?php
/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content">
 *
 * @link https:/images/developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package polidemika
 */

?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
	<meta charset="<?php bloginfo( 'charset' ); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<link rel="profile" href="https:/images/gmpg.org/xfn/11">
	<?php wp_head(); ?>
    <script src="<?php echo get_template_directory_uri(); ?>/js/mask.js"></script>
    <link rel="stylesheet" type="text/css" href="<?php echo get_template_directory_uri(); ?>/js/slick.css"/>
<style>
	#popmake-2258 .popmake-content, #popmake-2258 .popmake-content p, #popmake-2258 .popmake-content p img { height: 100%; }
	#popmake-2258 .popmake-content p img { width: 100%; object-fit: contain; }
	
	.block-name { margin-bottom: 16px; line-height: 20px; }
	.block-title { line-height: 39px; }
	h1.entry-title { font-size: 32px; margin: 15px 0 30px; }
	
	.doctor-appointment-card .team__card .card-info { display: block; }
	
	.site-header { padding-bottom: 20px; }
	.header-top { padding: 20px 0 0; gap: 20px; }
	.header-top__logos { flex-grow: 1; }
	.header-top__logos .low-vision { border-radius: 12px; }
	.header-top__info { gap: 20px; min-width: auto; }
	.header-top__info a.button, .header-top__info button { padding: 9px 12px; }
	.header-top__info .home-visit a button { border-color: rgba(243, 244, 247, 1); }
	.header-top__info .header-top__login a.button {	padding: 10px 12px; }
	.header-top__menu { flex-grow: 1; }
	.header-top__menu ul a:hover, .header-top__menu ul span:hover, .header-top__menu ul a.active { color: rgb(10 85 166); text-decoration: none; }
	.header-top__appointment button { border: none; }
	.header-top__menu ul { gap: 10px; }
	.header-top__menu ul a, .header-top__menu ul span { padding: 10px 20px; display: block; }
	.header-top__menu ul span { cursor: pointer; }
	
	.header-top .search-container form { margin: 0; }
	
	#masthead .container { position: relative; }
	.header-top__menu ul li > div { opacity: 0; position: absolute; pointer-events: none; width: 100%; padding: 40px 20px 20px; box-shadow: 0px 20px 30px 0px #0000000D; transform: translateY(50px); transition: all .25s ease-out; background: #fff; display: flex; align-items: center; }
	.header-top__menu ul li > div:after { position: absolute; top: 19px; left: 0; height: 2px; width: 100%; background: #78a9e6; content: ""; }
	.header-top__menu ul li > div ul { column-count: 3; width: 80%; gap: 0; display: block; }
	.header-top__menu ul li:hover div { opacity: 1; pointer-events: auto; transform: translateY(0px); }
	.header-top__menu ul li ul li {  }
	.header-top__menu ul li ul li a { padding: 7px 15px; }
	
	.red_bttn button { background: linear-gradient(207.24deg, #fb7474 -7.82%, #d90f0f 104.72%); border-radius: 12px; padding: 6px 10px; font-size: 14px; color: #fff; height: 40px; border: none; }

	.popmake-content form textarea { width: 100%; padding: 16px 24px; margin-bottom: 10px; }
	
	.main-banner { height: auto; background-position: right; background-size: contain; }
	.main-banner__wrapper { margin-bottom: 40px; }
	.main-banner__title { line-height: 58px; }
	.main-banner__subtitle { line-height: 39px; }
	.main-banner__text { line-height: 22px; }
	.main-banner__button button { padding: 6px 20px; border: none; }
	
	.block-title.accii { display: none; }
	.card-foto img { object-fit: cover; }
	.team__card .card-info { display: block; }
	.card-info__text { width: 100%; display: flex; justify-content: space-between; align-items: start; }
	.card-info__position a { white-space: nowrap; }
	.card-info__position { overflow: hidden; text-overflow: ellipsis; }
	.block-f { align-items: end; }
	.block-link a { font-size: 16px; font-weight: 600; line-height: 19.5px; letter-spacing: 2px; text-align: right; text-underline-position: from-font; text-decoration-skip-ink: none; }
	
	footer .container { padding: 60px 0 90px; }
	footer .container .footer_info .text { margin-top: 20px; }
	footer .container .footer_info .copyright { font-size: 14px; line-height: 17px; }
	footer .container ul.footer_menu li a { font-size: 14px; line-height: 20px; }
	
	.contacts__socials a img { max-width: 40px; height: 25px; }
	
	.diploms__image { width: 380px; }

	.services__list ul li a:hover { text-decoration: none; color: rgb(10 85 166); }
		
	.conts_form { display: flex; background: #fff; border-radius: 0 12px 12px 0; margin-bottom: 80px; }
	.cf_conts { background: rgba(74, 141, 237, 1); color: #fff; display: flex; justify-content: center; align-items: center; height: 556px; width: 40%; border-radius: 0 12px 12px 0; font-size: 18px; }
	.cf_conts p, .cf_soc_seti { margin: 8px 0; }
	.cf_form { display: flex; justify-content: center; align-items: center; height: 556px; width: 60%; }
	.cf_form form, .popmake-content form { display: flex; flex-direction: column; align-items: center; }
	.cf_form form p, .popmake-content form p { text-align: center; }
	.cf_form form input, .popmake-content form input { margin-bottom: 10px; background: rgba(243, 244, 247, 1); border-radius: 12px; border: none; outline: none; padding: 16px 24px; color: #011424; }
	button.bttn_left { margin: 60px 0 24px; border: 1px solid #AEB3C0; border-radius: 12px; padding: 6px 45px; font-size: 14px; background: linear-gradient(207.24deg, #74ACFB -7.82%, #0F61D9 104.72%); color: #fff; height: 55px; }
	button.bttn_left img { margin-right: 10px; }
	.cf_form span, .popmake-content span { color: rgba(174, 179, 192, 1); font-size: 14px; font-weight: 500; line-height: 17.07px; text-align: center; text-underline-position: from-font; text-decoration-skip-ink: none; }
	.cf_form form label, .cf_form form input, .popmake-content form label, .popmake-content form input { width: 100%; }
	.cf_form .block-title { line-height: 1; margin-bottom: 30px; }
		
	button { outline: none!important; }
	button:hover, button:active { border-color: #AEB3C0; outline: none!important; }
	
	.container { padding: 0px; }

@media screen and (min-width: 800px) {
	header#masthead { height: 146px; transition: all .3s ease-out; }
	header#masthead * { transition: all .3s ease-out; }
	header#masthead.scrolled { height: 84px;
		.header-top__search_wrapper, .header-top__info, .low-vision, .jaloba { display: none; }
		.container { display: flex; justify-content: space-between; }
		.header-top__menu ul li > div { left: 0; }
	}
}
    
    
    
#header_menu_mob { position: fixed; top: 0px; left: 0; right: 0; bottom: 0; width: 100%; transform: translateX(-100%); flex-direction: column; justify-content: space-between; align-items: center; display: flex; z-index: 22; background: #fff; transition: all 0.4s; padding: 76px 0 20px; gap: 20px; }
#header_menu_mob .menu_line { display: flex; flex-grow: 1; flex-direction: column; align-items: center; justify-content: center; padding: 0; margin: 0; list-style: none; gap: 10px; }
#header_menu_mob .menu_line a, #header_menu_mob .menu_line span { font-family: "Legion", Arial, serif; font-weight: 500; font-size: 16px; display: block; padding: 0px; color: #000; text-decoration: none; position: relative; }
#header_menu_mob .menu_line a:hover { color: rgb(10 85 166); }
#header_menu_mob.open { transform: translateX(0); }
#header_menu_mob .head-info { display: flex; align-items: center; flex-direction: column-reverse; gap: 10px; justify-content: center; }
#header_menu_mob .head-info a { text-decoration: none; font-size: 16px; }
#header_menu_mob .head-info span { text-align: center; }
#container_cross, #header_menu_mob { display: none; }
.container_cross { cursor: pointer; padding: 12px 10px; border-radius: 12px; background: rgba(245, 246, 250, 1); }
.bar1, .bar2, .bar3 { width: 20px; height: 2px; background-color: rgba(1, 20, 36, 1); margin: 0 0 5px; transition: 0.4s; transform-origin: center; border-radius: 2px; }
.bar3 { margin-bottom: 0; }
.change .bar1 { -webkit-transform: rotate(-45deg) translate(-3px, 5px); transform: rotate(-45deg) translate(-3px, 5px); }
.change .bar2 {opacity: 0;}
.change .bar3 { -webkit-transform: rotate(45deg) translate(-5px, -7px); transform: rotate(45deg) translate(-5px, -7px); }
#phone_head.remove { display: none; }
.appointment .time:hover { color: #fff; background-image: url(<?php echo get_template_directory_uri(); ?>/assets/images/check_w.svg), linear-gradient(rgba(116, 172, 251, 1), rgba(116, 172, 251, 1)); background-position: right 10px center, center; }
#mobmenu_usl span { margin: 0 auto; width: fit-content; }
#mobmenu_usl span img { transform: rotate(45deg); transition: all .2s ease .3s; }
#mobmenu_usl ul { padding: 0px; border-radius: 10px; margin: 0px 0; list-style: none; height: 0; transition: all .25s ease; overflow: hidden; display: flex; flex-direction: column; gap: 10px; background: rgba(245, 246, 250, 1); }
#header_menu_mob #mobmenu_usl ul li a { text-align: left; font-size: 14px; }
#mobmenu_usl.show ul { height: 289px; padding: 10px; }
#mobmenu_usl.show span img { transform: rotate(0deg); }
	
.team { justify-content: left; }
.team__card .card-foto { width: 100%; }
.card-foto img { width: 100%; height: 100%; }

.testimonial { margin-left: -10px; margin-right: -10px; margin-bottom: 80px; }
.testimonial__card { margin: 10px 10px 30px; box-shadow: 0px 10px 10px 0px #0000000D; }
.t-card__photo { border-radius: 50%; overflow: hidden; }
.slick-arrow { font-size: 0; border: 0; background: #fff; border-radius: 12px; width: 44px; height: 40px; border: none; background-position: center; background-repeat: no-repeat; position: absolute; top: -80px; }
.slick-prev { background-image: url(<?php echo get_template_directory_uri(); ?>/assets/images/nav-left-arrow.svg); right: 60px; }
.slick-next { background-image: url(<?php echo get_template_directory_uri(); ?>/assets/images/nav-right-arrow.svg); right: 10px; }
.slick-dots { padding: 0; display: flex; lisy-style: none; gap: 10px; justify-content: center; }
.slick-dots li::marker { display: none; content: none; }
.slick-dots li button { display: block; font-size: 0; height: 6px; border: none; background: rgba(174, 179, 192, 1); border-radius: 6px; width: 20px; transition: all .2s ease; }
.slick-dots li.slick-active button { background: rgba(15, 97, 217, 1); width: 60px; }

footer .container .footer_info { max-width: 360px; width: auto; }
	
.pum-theme-987 .pum-content, .pum-theme-enterprise-blue .pum-content { font-weight: 500; }
.pum-content {padding: 20px}
	
.bvi-widget, .bvi-shortcode a, .bvi-widget a, .bvi-shortcode { background: transparent; border: none; }
	.bvi-shortcode a, .bvi-widget a { border: none; }
	.bvi-shortcode { padding: 0; }
	.bvi-open { display: block; width: auto; height: auto; padding: 0; position: relative; }

	
#now_reading { display: flex; gap: 30px; justify-content: left; }

	.search .arch_one_wrap { width: 100%; }
	.search article { width: 100%; }
	
	
	
	
	
	.bvi-active {
		.header-top__search_wrapper { display: none; }
		.header-top__appointment { align-self: auto; }
		.header-top__appointment button, .red_bttn button, .main-banner__button button { background: unset; height: auto; }
		.slick-arrow { width: auto; height: auto; background: none; }
	}
	
@media screen and (max-width: 1100px) {
	
	.header-top__search_wrapper {
        width: 40px;
    }
	.header-top__search_wrapper.search-expanded {
        width: 100%;
        max-width: 100%
    }
		.header-top__logos.search-expanded {
        width: 100%;
        max-width: 100%
    }
}
	
@media screen and (max-width: 850px) {
	.header-top__menu ul a, .header-top__menu ul span { padding: 10px 8px; }
	.header-top__info { display: none; }
	
	.header-top__appointment button { font-size: 0px; }
	.header-top__appointment button img { margin-right: 0px; }

}
	.pum-theme-987 .pum-container, .pum-theme-enterprise-blue .pum-container { padding: 0; }
	#popmake-2258 .popmake-content p img { max-width: 80vw; max-height: 80vh; }
	
@media screen and (max-width: 640px) {
	#popmake-2258 .popmake-content p img { max-width: 90vw; max-height: 90vh; }
	.container { padding: 80px 10px 60px; }
	.page-id-3 .entry-title { font-size: 24px; }
	
	#now_reading { flex-wrap: wrap; padding: 20px; }
	#primary #now_reading article { width: 100%; }
	
	.header-top__appointment button { font-size: 14px; }
	.header-top__appointment button img { margin-right: 10px; }

	#masthead { position: fixed; top: 0; left: 0; right: 0; z-index: 33; padding: 20px 16px; }
    #masthead .container { padding: 0; }
	#container_cross { display: block; }
	#header_menu_mob { display: flex; }
	.pc, .low-vision.pc { display: none; }
    .header-top { padding: 0; }
    .header-top__info .header-top__login a.button { font-size: 0; padding: 19px 11px; border: 0; background: rgba(245, 246, 250, 1); }
    .header-top__info .header-top__login a.button img { margin: 0; }
    .main-banner { padding-top: 330px; background-position: center top; height: auto; background-size: 100%; }
    .main-banner__wrapper { text-align: center; margin: 0 auto; }
    .main-banner__title { font-size: 24px; line-height: 30px; margin: 0 0 6px; }
    .main-banner__subtitle { font-size: 20px; line-height: 24px; font-weight: 700; margin: 0 0 24px; }
    .main-banner__text { font-size: 16px; line-height: 20px; font-weight: 500; margin: 0 0 30px; }
    .main-banner__button { margin: 0; }
    .main-banner__button a button { padding: 15px 20px; font-size: 16px; }
    .main-container > .container > .container { padding: 60px 16px 30px; }
    .block-name { font-size: 12px; line-height: 15px; margin-bottom: 14px; text-align: center; }
    .block-title { font-size: 24px; line-height: 30px; font-weight: 800; text-align: center; }
    .services { padding: 10px; margin: 0; }
    .services__list ul { column-count: 1; }
    .mt20 { margin: 0; }
    .block-link { display: none; }
    .block-f { justify-content: center; }
    .team__card { flex-direction: row; padding: 16px; height: auto; width: 100%; flex-wrap: wrap; justify-content: end; }
    .team__card .card-foto { height: auto; width: 100px; height: 110px; margin-right: 14px; }
    .team__card .card-buttons { width: calc(100%); justify-content: left; gap: 20px; margin-top: 15px; }
    .team__card .card-info { flex-direction: column-reverse; margin: 0; }
    .card-info__rating { margin-bottom: 13px; }
    .team__card .card-info__fio { font-size: 16px; font-weight: 600; line-height: 18px; }
    .card-info__text { width: 100%; display: block; }
    .team__card .card-info__position { font-size: 16px; line-height: 16px; }
    .team__card .card-info { width: calc(100% - 120px); }
    .card-foto img { width: 100%; height: 100%; }
    .testimonial__card { width: 100%; }
    .diploms { gap: 10px; }
    .diploms__image { width: 48%; }
    footer .container { flex-direction: column; gap: 20px; padding: 50px 16px; }
    .contacts { padding: 0; }
    .appointment { width: auto; padding: 20px 15px; }
    .appointment .f-between { flex-wrap: wrap; gap: 20px; }
    .appointment .f-between .appointment__block { width: 100%; }
    #datepicker { width: 100%; }
    .appointment .f-between .appointment__block .ui-widget-content { width: 100%; padding: 15px !important; width: 100% !important; }
    .page-template-page-spec .block-f { flex-direction: column; align-items: center;  gap: 20px; }
    .block-f form {  }
    .appointment__block, .appointment__block form { width: 100%; }
    article { flex-wrap: wrap; gap: 20px; }
    article .post-thumbnail, article .arch_one_wrap { width: 100%; }
    .page-header .page-title { margin: 100px 20px 40px; }
    .single article { padding: 0 20px; }
    .diw_top_info { flex-wrap: wrap; justify-content: center; gap: 20px; }
    .diw_top_info .diw_photo { width: 100%; max-width: 380px; }
    .diwi_right { width: 100%; }
    .single-doctor .main-container { padding: 0 20px; }
    .doc_in_wrap .diw_tabs .diw_header { gap: 10px; flex-wrap: wrap; justify-content: flex-start; }
    .doc_in_wrap .diw_tabs .diw_info { padding: 0; }
    .doc_in_wrap .diw_tabs .diw_header-tab { padding: 5px 10px; margin: 0; }
    .page-template-page-docs .doc_wrap { padding: 20px; }
    .page-template-page-docs .one_doc { flex-wrap: wrap; }
    .page-template-page-docs .block-f { padding: 100px 0 40px; }
	.page-template-page-cont .wrap_offices { grid-template-columns: 1fr; }
	.page-template-page-cont .conts_form, .page-template-page-spec_sel .conts_form, .page-template-page-menu_uslugi .conts_form, .page-template-page-stat_and_form .conts_form { flex-wrap: wrap; }
	.page-template-page-cont .conts_form .cf_conts,
	.page-template-page-cont .conts_form .cf_form,
	.page-template-page-menu_uslugi .cf_form,
	.page-template-page-stat_and_form .cf_form,
	.page-template-page-spec_sel .conts_form .cf_conts,
	.page-template-page-spec_sel .conts_form .cf_form,
	.page-template-page-menu_uslugi .conts_form .cf_form,
	.page-template-page-menu_uslugi .conts_form .cf_conts,
	.page-template-page-stat_and_form .conts_form .cf_conts,
	.page-template-page-stat_and_form .conts_form .cf_form { width: 100%; }
	.page-template-page-spec_sel #specialists {  }
	.page-template-page-menu_uslugi .diw_table { padding: 20px; }
	.page-template-page-menu_uslugi .diw_table a { width: 100%; }
	.staje { display: none!important; }
	.wpcf7-form p { padding: 0 20px; }
	.conts_form { margin-bottom: 0; }
}
</style>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>
<header  id="masthead" class="site-header"> 
	<div class="container">
	<div class="header-top"> 
    <div id="container_cross" class="container_cross mob" onclick="myFunction(this)">
  			<div class="bar1"></div>
  			<div class="bar2"></div>
  			<div class="bar3"></div>
	</div>
		<div class="header-top__logos"> 
			<div class="logo">
				<a href="<?php echo esc_url( home_url( '/' ) ); ?>">
					<img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/logo.svg">
				</a>
			</div>
		</div>
		<div class="header-top__search_wrapper">
			<div class="search-container" id="searchBox">
				<form role="search" method="get" id="searchform" class="searchform" action="<?php echo home_url( '/' ); ?>">
					<input type="text" value="" name="s" id="searchInput" class="search-input" placeholder="Поиск..." autocomplete="off">
					<button type="button" id="searchToggle" class="search-icon-btn">
						<svg class="search-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
							<path d="M460.355 421.59l-106.51-106.512c20.04-27.553 31.884-61.437 31.884-98.037C385.73 124.935 310.792 50 218.685 50c-92.106 0-167.04 74.934-167.04 167.04 0 92.107 74.935 167.042 167.04 167.042 34.912 0 67.352-10.773 94.184-29.158L419.945 462l40.41-40.41zM100.63 217.04c0-65.095 52.96-118.055 118.056-118.055 65.098 0 118.057 52.96 118.057 118.056 0 65.097-52.96 118.057-118.057 118.057-65.096 0-118.055-52.96-118.055-118.056z"></path>
						</svg>
					</button>
					<button type="submit" id="searchSubmit" class="search-icon-btn hide">
						<svg class="search-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
							<path d="M460.355 421.59l-106.51-106.512c20.04-27.553 31.884-61.437 31.884-98.037C385.73 124.935 310.792 50 218.685 50c-92.106 0-167.04 74.934-167.04 167.04 0 92.107 74.935 167.042 167.04 167.042 34.912 0 67.352-10.773 94.184-29.158L419.945 462l40.41-40.41zM100.63 217.04c0-65.095 52.96-118.055 118.056-118.055 65.098 0 118.057 52.96 118.057 118.056 0 65.097-52.96 118.057-118.057 118.057-65.096 0-118.055-52.96-118.055-118.056z"></path>
						</svg>
					</button>
				</form>
			</div>
		</div>
		<div class="header-top__info"> 
			<div class="header-top__phone pc"><a href="tel:+73462774877"><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/phone.svg" alt="">+7(3462)77-44-08</a></div>
			<div class="home-visit pc">
				<a href="<?php echo home_url(); ?>/appointment">
					<button> <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/home-visit.svg" alt="">Выезд на дом</button>
				</a>
			</div>
			<div class="header-top__login">
				<a class="button" href="<?php echo wp_login_url(); ?>">				
					<img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/login.svg" alt="">Вход</button>
				</a>
			</div>
		</div>
	</div>
	<div class="header-top pc"> 
		<div class="header-top__menu"> 
			<ul>
				<li><span><img style="height: 12px; margin-right: 5px;" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/menu_usl.svg"> Услуги</span>
					<div>
				<ul>
<?php
							
if( have_rows('verhnee_menyu_uslugi', 'option') ):
    while ( have_rows('verhnee_menyu_uslugi', 'option') ) : the_row();
        echo '<li><a href="' . get_sub_field('ssylka') . '">' . get_sub_field('nazvanie') . '</a></li>';
    endwhile;
endif;

?>
				</ul>
	<div>
		<div class="header-top__phone pc"><a href="tel:+73462774877"><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/phone.svg" alt="">+7(3462)77-44-08</a></div>
		<div class="home-visit pc">
				<a href="<?php echo home_url(); ?>/appointment">
					<button style="padding: 10px; border-color: rgba(243, 244, 247, 1); border-radius: 10px; "> <!--img style="margin-right: 10px;" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/phone.svg" alt=""-->Заказать звонок</button>
				</a>
			</div>
	</div>
					</div>
				</li>
				<li> <a class="menu-item__link" href="/akczii">Акции </a></li>
				<li> <a class="menu-item__link" href="/speczialisty">Специалисты</a></li>
				<li> <a class="menu-item__link" href="/category/novosti/">Новости</a></li>
				<li> <a class="menu-item__link" href="/kontakty">Контакты</a></li>
			</ul>
		</div>
		<div class="low-vision pc"><!--img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/lowvision.svg" alt=""-->
		
<div class="bvi-shortcode">
	<a href="#" class="bvi-open">
		<img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/lowvision.svg" alt="">
	</a>
</div>
		
		</div>
		<div class="header-top__appointment">
			<a href="<?php echo home_url(); ?>/appointment">
				<button><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/appointment.svg" alt="">Запись онлайн</button>
			</a>
		</div>
		<div class="red_bttn jaloba">
			<a href="<?php echo home_url(); ?>/appointment">
				<button>Замечания</button>
			</a>
		</div>
	</div>
	</div>
</header>

<div id="header_menu_mob">
		<ul class="menu_line">
			<li id="mobmenu_usl"> <span><img style="height: 12px; margin-right: 5px;" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/cross.svg"> Услуги </span>
				<ul>
<?php
							
if( have_rows('verhnee_menyu_uslugi', 'option') ):
    while ( have_rows('verhnee_menyu_uslugi', 'option') ) : the_row();
        echo '<li><a href="' . get_sub_field('ssylka') . '">' . get_sub_field('nazvanie') . '</a></li>';
    endwhile;
endif;

?>
				</ul>
			</li>
			<li> <a href="/akczii">Акции </a></li>
			<li> <a href="/speczialisty">Специалисты</a></li>
			<li> <a href="/category/novosti/">Новости</a></li>
			<li> <a href="/kontakty">Контакты</a></li>
		</ul>
		<div class="head-info">
        	<div class="header-top__appointment">
        	<a href="<?php echo home_url(); ?>/appointment">
				<button><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/appointment.svg" alt="">Запись онлайн</button>
			</a>
            </div>
            <div class="home-visit">
			<a href="<?php echo home_url(); ?>/appointment">
				<button> <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/home-visit.svg" alt="">Выезд на дом</button>
			</a>
			</div>
			<div class="red_bttn jaloba">
			<a href="<?php echo home_url(); ?>/appointment">
				<button>Замечания</button>
			</a>
			</div>
		</div>
        <div class="header-top__phone"><a href="tel:+73462774877"><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/phone.svg" alt="">+7(3462)77-44-08</a></div>
</div>

<script>
function myFunction(x) {
    x.classList.toggle("change");
	document.getElementById('header_menu_mob').classList.toggle("open");
	document.getElementById('phone_head').classList.toggle("remove");
}
const elements = document.querySelectorAll('.mob_menu_a');
for (let elem of elements) {
    elem.onclick = function() {
        document.getElementById('header_menu_mob').classList.toggle("open");
    };
}


jQuery(function($) {	
	$(document).ready(function () {
		// эта функция получает наш результат проверки орфографии энтер
		fix_spell = function (data) {
			data.forEach(function (elem) {
				// она находит наше поле ввода по имени
				$('#searchInput').val(
					// и меняет всё на правильные слова без ошибок
					$('#searchInput').val().replace(
						elem['word'],
						elem['s'][0] || elem['word']
						)
					)
			});
			$('#searchSubmit').click();
		}
		
		// эта функция получает наш результат проверки орфографии пробел
		fix_spell2 = function (data) {
			data.forEach(function (elem) {
				// она находит наше поле ввода по имени
				$('#searchInput').val(
					// и меняет всё на правильные слова без ошибок
					$('#searchInput').val().replace(
						elem['word'],
						elem['s'][0] || elem['word']
						)
					)
			});
		}
	});
	// обработчик нажатия на клавиши
	document.addEventListener('keydown', function (e) {
		// если нажат энтер
		if ((e.keyCode == 13)) {
			event.preventDefault();
			// делим текст на строки
			var lines = $('#searchInput').val().replace(/\r\n|\n\r|\n|\r/g, "\n").split("\n");
			// и обрабатываем каждую строчку:
			lines.forEach(function (line) {
				if (line.length) {
					// отправляем строку со словами на проверку в Спеллер, результат сразу отправляется в функцию fix_spell
					$.getScript('https://speller.yandex.net/services/spellservice.json/checkText?text=' + line + '&callback=fix_spell');
				}
			});
		//	$('#searchSubmit').click();
		}
		// если нажат пробел
		if ((e.keyCode == 32)) {
			// делим текст на строки
			var lines = $('#searchInput').val().replace(/\r\n|\n\r|\n|\r/g, "\n").split("\n");
			// и обрабатываем каждую строчку:
			lines.forEach(function (line) {
				if (line.length) {
					// отправляем строку со словами на проверку в Спеллер, результат сразу отправляется в функцию fix_spell
					$.getScript('https://speller.yandex.net/services/spellservice.json/checkText?text=' + line + '&callback=fix_spell2');
				}
			});
		}
	});

});


document.getElementById('mobmenu_usl').onclick = openUL;
function openUL() {
  document.getElementById('mobmenu_usl').classList.toggle('show');
}
	
	
const currentLocation = location.href;
const menuItem = document.querySelectorAll('.menu-item__link');
	console.log(currentLocation);
const menuLenght = menuItem.length
for (let i = 0; i<menuLenght; i++) {
    if (currentLocation.includes(menuItem[i].href)) {
        menuItem[i].className = "active menu-item__link";
    }
}
</script>
<div class="main-container mt20">
	<div class="container">