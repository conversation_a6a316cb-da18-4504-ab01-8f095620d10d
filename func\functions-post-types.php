<?php
defined( 'ABSPATH' ) || die();

function register_faq_post_type() {
	register_post_type(
		'faq',
		array(
			'labels'        => array(
				'name'          => __( 'Вопрос-Ответ' ),
				'singular_name' => __( 'Вопрос-Ответ' ),
				'add_new'       => __( 'Добавить новый Вопрос-Ответ' ),
				'add_new_item'  => __( 'Добавить новый Вопрос-Ответ' ),
				'edit_item'     => __( 'Редактировать Вопрос-Ответ' ),
				'new_item'      => __( 'Новый Вопрос-Ответ' ),
				'view_item'     => __( 'Посмотреть Вопрос-Ответ' ),
				'search_items'  => __( 'Найти Вопрос-Ответ' ),
			),
			'public'        => true,
			'has_archive'   => false,
			'menu_position' => 20, // Show it right after the Posts menu
			'supports'      => array( 'title', 'editor' ),
			'rewrite'       => array( 'slug' => 'faq' ),
		)
	);
}
add_action( 'init', 'register_faq_post_type' );

function create_awards_post_type() {
	$labels = array(
		'name'               => 'Грамоты',
		'singular_name'      => 'Грамота',
		'menu_name'          => 'Грамоты',
		'name_admin_bar'     => 'Грамота',
		'add_new'            => 'Добавить новую',
		'add_new_item'       => 'Добавить новую грамоту',
		'edit_item'          => 'Редактировать грамоту',
		'new_item'           => 'Новая грамота',
		'view_item'          => 'Просмотреть грамоту',
		'search_items'       => 'Искать грамоты',
		'not_found'          => 'Грамоты не найдены',
		'not_found_in_trash' => 'В корзине грамот не найдено',
	);

	$args = array(
		'labels'        => $labels,
		'public'        => true,
		'has_archive'   => true,
		'supports'      => array( 'title', 'thumbnail' ), // Только заголовок и миниатюра
		'menu_position' => 22,
		'menu_icon'     => 'dashicons-awards', // Иконка для меню
		'rewrite'       => array( 'slug' => 'awards' ), // ЧПУ
	);

	register_post_type( 'awards', $args );
}

add_action( 'init', 'create_awards_post_type' );


// Добавление нового типа записи 'Отзывы'
function create_reviews_post_type() {
	register_post_type(
		'reviews',
		array(
			'labels'        => array(
				'name'               => __( 'Отзывы', 'textdomain' ),
				'singular_name'      => __( 'Отзыв', 'textdomain' ),
				'add_new'            => __( 'Добавить отзыв', 'textdomain' ),
				'add_new_item'       => __( 'Добавить новый отзыв', 'textdomain' ),
				'edit_item'          => __( 'Редактировать отзыв', 'textdomain' ),
				'new_item'           => __( 'Новый отзыв', 'textdomain' ),
				'view_item'          => __( 'Просмотреть отзыв', 'textdomain' ),
				'search_items'       => __( 'Искать отзывы', 'textdomain' ),
				'not_found'          => __( 'Отзывы не найдены', 'textdomain' ),
				'not_found_in_trash' => __( 'Отзывы в корзине не найдены', 'textdomain' ),
			),
			'public'        => true,
			'has_archive'   => false,
			'rewrite'       => array( 'slug' => 'reviews' ),
			'supports'      => array( 'title' ),
			'menu_position' => 21,
			'menu_icon'     => 'dashicons-testimonial',
		)
	);
}
add_action( 'init', 'create_reviews_post_type' );


function register_doctor_post_type() {
	$args = array(
		'labels'        => array(
			'name'               => __( 'Врачи', 'textdomain' ),
			'singular_name'      => __( 'Врач', 'textdomain' ),
			'add_new'            => __( 'Добавить врача', 'textdomain' ),
			'add_new_item'       => __( 'Добавить нового врача', 'textdomain' ),
			'edit_item'          => __( 'Редактировать врача', 'textdomain' ),
			'new_item'           => __( 'Новый врач', 'textdomain' ),
			'view_item'          => __( 'Просмотреть врача', 'textdomain' ),
			'search_items'       => __( 'Искать врача', 'textdomain' ),
			'not_found'          => __( 'Врачи не найдены', 'textdomain' ),
			'not_found_in_trash' => __( 'Врачи в корзине не найдены', 'textdomain' ),
		),
		'public'        => true,
		'has_archive'   => true,
		'supports'      => array( 'title', 'custom-fields' ),
		'rewrite'       => array( 'slug' => 'doctors' ),
		'menu_position' => 23,
		'menu_icon'     => 'dashicons-businesswoman',
	);
	register_post_type( 'doctor', $args );
}
add_action( 'init', 'register_doctor_post_type' );

// Таксономия для услуг врачей
function register_doctor_service_taxonomy() {
	register_taxonomy(
		'service',
		'doctor',
		array(
			'labels'            => array(
				'name'          => 'Услуги',
				'singular_name' => 'Услуга',
			),
			'hierarchical'      => false,
			'show_admin_column' => true,
			'rewrite'           => array( 'slug' => 'service' ),
		)
	);
}
add_action( 'init', 'register_doctor_service_taxonomy' );


function custom_post_type_jobtitle() {
	$args = array(
		'label'               => __( 'Должности', 'textdomain' ),
		'public'              => false,
		'show_ui'             => true,
		'show_in_menu'        => true,
		'supports'            => array( 'title' ),
		'has_archive'         => false,
		'exclude_from_search' => true,
		'publicly_queryable'  => false,
		'show_in_nav_menus'   => false,
		'capability_type'     => 'post',
	);

	register_post_type( 'jobtitle', $args );
}
add_action( 'init', 'custom_post_type_jobtitle' );


// Добавление нового типа записи 'Акции'
function create_accii_post_type() {
	register_post_type(
		'accii',
		array(
			'labels'        => array(
				'name'               => __( 'Акции', 'textdomain' ),
				'singular_name'      => __( 'Акция', 'textdomain' ),
				'add_new'            => __( 'Добавить акцию', 'textdomain' ),
				'add_new_item'       => __( 'Добавить новую акцию', 'textdomain' ),
				'edit_item'          => __( 'Редактировать акцию', 'textdomain' ),
				'new_item'           => __( 'Новая акция', 'textdomain' ),
				'view_item'          => __( 'Просмотреть акцию', 'textdomain' ),
				'search_items'       => __( 'Искать акции', 'textdomain' ),
				'not_found'          => __( 'Акции не найдены', 'textdomain' ),
				'not_found_in_trash' => __( 'Акции в корзине не найдены', 'textdomain' ),
			),
			'public'        => true,
			'has_archive'   => false,
			'rewrite'       => array( 'slug' => 'accii' ),
			'supports'      => array( 'title' ),
			'menu_position' => 22,
			'menu_icon'     => 'dashicons-testimonial',
		)
	);
}
add_action( 'init', 'create_accii_post_type' );
