<?php
/**
 * The template for displaying search results pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#search-result
 *
 * @package polidemika
 */

get_header();
?>

	<main id="primary" class="site-main">

		<?php if ( have_posts() ) : ?>

			<header class="page-header">
				<h1 class="page-title">
					<?php
					/* translators: %s: search query. */
					printf( esc_html__( 'Результаты поиска для: %s', 'polidemika' ), '<span>' . get_search_query() . '</span>' );
					?>
				</h1>
			</header><!-- .page-header -->
<div class="team" style="display: flex;">
			<?php
			/* Start the Loop */
			while ( have_posts() ) :
				the_post();

				/**
				 * Run the loop for the search to output the results.
				 * If you want to overload this in a child theme then include a file
				 * called content-search.php and that will be used instead.
				 */
		?>
		
<?php 
	$rrr = $post->post_type;
	if ( $rrr == 'doctor') { ?>

	
		
			<div class="team__card" data-term="<?php $pos = get_first_doctor_position( get_the_ID() ); echo $pos ? $pos->term_id : ''; ?>">
                <div class="card-foto">
                    <img src="<?php echo get_doctor_photo( get_the_ID() ); ?>">
                </div>
                <div class="card-info"> 
                    <div class="card-info__text">
                    <div class="card-info__fio">
                        <?php echo get_doctor_fio( get_the_ID() ); ?>
                    </div>
                    <div class="card-info__rating">
                    <div class="stars"> </div>
                    <div class="points">4.8</div>
                    </div>
                    </div>
					<div class="card-info__position">
                        <?php
                            $position = get_first_doctor_position( get_the_ID() );
                            if ( $position ) {
                                echo $position->name;
                            }
                        ?>
                        
                    </div>
                    
                </div>
                <div class="card-buttons">
                    <?php
                        $hashItems = array();
                        // Код направления
                        if($doctor_position = term_description($position)) {
                            $doctor_position = strip_tags($doctor_position);
                            $hashItems[] = "p:{$doctor_position}";
                        }
                        // Код врача
                        if($doctor_code = get_post_meta($doctor->ID, 'code', true)) {
                            $doctor_code = str_pad($doctor_code, 11, '0', STR_PAD_LEFT);
                            $hashItems[] = "d:{$doctor_code}";
                        }
                    ?>
                    <a href="<?php echo home_url(); ?>/appointment/#<?= implode('_', $hashItems); ?>">
                        <button><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/appointment-blue.svg" alt="">Записаться на прием</button>
                    </a>
                    <a class="card-link" href="<?php the_permalink(); ?>">Подробнее</a>
                </div>
            </div>		
		<?php } ?>	

		<?php if ( $rrr == 'post') { get_template_part( 'template-parts/content_arch', get_post_type() );	}
	
		if ( $rrr == 'page') { get_template_part( 'template-parts/content_arch', get_post_type() );	}
	
			endwhile;

			the_posts_navigation();

		else :

			get_template_part( 'template-parts/content', 'none' );

		endif;
		?>
		</div>
	</main><!-- #main -->

<?php
get_sidebar();
get_footer();

