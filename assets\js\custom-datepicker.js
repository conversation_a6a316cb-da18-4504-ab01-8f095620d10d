jQuery(document).ready(function ($) {
    // Постоянно видимый календарь
    $('#datepicker').datepicker({
        dateFormat: 'yy-mm-dd', // Формат даты
        minDate: 0, // Не позволяет выбрать даты в прошлом
        onSelect: function (dateText) {
            // Сохраняем выбранную дату в скрытом поле
            $('#appointment_date').val(dateText);
        },
    });

    // $('#birth_date').datepicker({
    //     dateFormat: 'yy-mm-dd', // Формат даты
    //     minDate: 0, // Не позволяет выбрать даты в прошлом
    // });
});