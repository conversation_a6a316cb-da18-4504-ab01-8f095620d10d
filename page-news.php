<?php
/**
/* Template Name: News Page
 *
 * @package polidemika
 */

get_header();
?>
<style>
.__select { position: relative; width: 280px; height: 40px; margin: 0 auto;
	&[data-state="active"] {
    .__select__title { &::before { transform: translate(-3px, -50%) rotate(-45deg); }
				      &::after { transform: translate(3px, -50%) rotate(45deg); }
    				}
    .__select__content { opacity: 1; }
    .__select__label + .__select__input + .__select__label { max-height: 40px; border-top-width: 1px; }
  																									
		}
.__select__title { display: flex; align-items: center; width: 100%; height: 100%; padding: 8px 24px; border-radius: 12px;cursor: pointer; background: #fff;
	&::before, &::after { content: ""; position: absolute; top: 50%; right: 16px; display: block; width: 10px; height: 2px; transition: all 0.3s ease-out; background-color: rgba(174, 179, 192, 1); transform: translate(-3px, -50%) rotate(45deg); }
  	&::after { transform: translate(3px, -50%) rotate(-45deg); }
	&:hover {
		&::before, &::after { background-color: #1062D9; }
  	}
	}
.__select__content { position: absolute; top: 40px; left: 3px; display: flex; flex-direction: column; width: calc(100% - 6px); background-color: #ffffff; border-radius: 0 0 12px 12px; transition: all 0.3s ease-out; opacity: 0; z-index: 8; overflow: hidden; }
.__select__input { display: none;
	&:checked + label { background-color: #dedede; }
  &:disabled + label { opacity: 0.6; pointer-events: none; }
}
.__select__label { display: flex; align-items: center; width: 100%; height: 40px; max-height: 0; padding: 0 16px; transition: all 0.2s ease-out; cursor: pointer; overflow: hidden;
	& + input + & { border-top: 0 solid #C7CCD160; }
  &:hover { background-color: #1062D9 !important; color: #ffffff; }
}
</style>
      
<?php
/**
 * Специалисты на главной
 */

$args = array(
	'post_type'      => 'doctor',
	'numberposts' => 261,
);

$doctors = get_posts( $args );
//update_doctors_from_xml();
?>

<div class="container mt20" id="specialists"> 
    <div class="block-name">Наша комманда</div>
    <div class="block-f">
        <div class="block-title">Наши медработники</div>
<form>
  <div class="__select" data-state="">
    <div class="__select__title" data-default="Выберите направление">Выберите направление</div>
    <div class="__select__content">
<?php
$categories = get_categories( [
	'taxonomy' => 'position',
	'orderby' => 'name',
	'order' => 'ASC'
] );
$i == 0;
foreach( $categories as $category ){ 
	$i++; ?>
	<input id="singleSelect<?php echo $i; ?>" class="__select__input" type="radio" name="singleSelect" />
	<label for="singleSelect<?php echo $i; ?>" class="__select__label"><?php echo $category->name; ?></label>
<?php } ?>
    </div>
  </div>
</form>
<script>
const selectSingle = document.querySelector('.__select');
const selectSingle_title = selectSingle.querySelector('.__select__title');
const selectSingle_labels = selectSingle.querySelectorAll('.__select__label');
selectSingle_title.addEventListener('click', () => {
  if ('active' === selectSingle.getAttribute('data-state')) {
    selectSingle.setAttribute('data-state', '');
  } else {
    selectSingle.setAttribute('data-state', 'active');
  }
});
for (let i = 0; i < selectSingle_labels.length; i++) {
  selectSingle_labels[i].addEventListener('click', (evt) => {
    selectSingle_title.textContent = evt.target.textContent;
    selectSingle.setAttribute('data-state', '');
  });
}	
</script>
		
    </div>
    <div class="team">
        <?php foreach ( $doctors as $doctor ) : ?>
            <div class="team__card">
                <div class="card-foto">
                    <img src="<?php echo get_doctor_photo( $doctor->ID ); ?>">
                </div>
                <div class="card-info"> 
                    <div class="card-info__text">
                    <div class="card-info__fio">
                        <?php echo get_doctor_fio( $doctor->ID ); ?>
                    </div>
                    <div class="card-info__position">
                        <?php
                            $position = get_first_doctor_position( $doctor->ID );
                            if ( $position ) {
                                echo '<a href="' . get_term_link( $position ) . '">' . $position->name . '</a>';
                            }
                        ?>
                        
                    </div>
                    </div>
                    <div class="card-info__rating">
                    <div class="stars"> </div>
                    <div class="points">4.8</div>
                    </div>
                </div>
                <div class="card-buttons"> 
                    <a href="<?php echo home_url(); ?>/appointment">
                        <button><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/appointment-blue.svg" alt="">Записаться на прием</button>
                    </a>
                    <a class="card-link" href="<?php echo $doctor->guid; ?>">Подробнее</a>
                </div>
            </div>
        <?php endforeach; ?>
        
    </div>
</div>
<? wp_reset_postdata(); ?>
      

    </div>
<?php
get_footer();
