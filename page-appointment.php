<?php
/**
 * The template for displaying the front page.php
 *
 * @package polidemika
 */

get_header();
?>
<style>
@media screen and (max-width: 640px) {
	input[type='date'] { -webkit-appearance: initial; -moz-appearance: initial; appearance: initial; }
	input[type='date']:before { color: #aaa; content: attr(placeholder); white-space: nowrap; }
	}
</style>
		<div class="container" id="appointment_container"> 
		<div class="block-name center mt20">Запись</div>
		<div class="block-title center">Записаться на прием онлайн</div>
		<div class="appointment" id="appointment">
			<div class="f-between f-align-end">
				<div class="appointment__block">
					<label>Введите дату рождения пациента для продолжения</label>
					<input type="date" placeholder="Дата рождения" id="birth_date" name="birth_date" style="color: gray;" >
				</div>
				<div class="appointment__block">
          <select name="position" id="position_select" style="display: none; color: gray;" disabled>
            <option selected value="">Выберите направление</option>
            <?php
            $positions = get_all_positions();
            foreach ( $positions as $position ) {
              if ( ! in_array( $position->term_id, array( 63 ) ) ) { // Скрываем ненужные направления
                $linked_categories = get_field( 'linked_categories', $position );
                $is_children       = in_array( 60, $linked_categories );
                echo '<option value="' . (int) $position->description . '" style="color: black;" data-children="' . ( $is_children ? 'true' : 'false' ) . '">' . $position->name . '</option>';
              }
            }
            ?>
            </select>
				</div>
			</div>
		</div>
		</div>
		<!-- Затемняющий фон -->
		<div id="overlay" style="display: none;"></div>

		<!-- Модальное окно -->
		<div id="loadingModal" style="display: none;">
			<div class="modal-content">
				<p>Загрузка...</p>
				<img src="https://media.tenor.com/On7kvXhzml4AAAAi/loading-gif.gif" alt="Загрузка..." style="width: 50px; height: 50px;"/>
			</div>
		</div>
<?php
get_footer();
