<?php
// ekassir_qr_fixed.php

// ==== НАСТРОЙКИ ====
$privateKeyPath = __DIR__ . '/certs/LB0000011859-ips-sysuser-staging-client.key';
$issuer = "https://c2b.sngb.ru/";
$subject = "ac-*********";
$audience = "https://c2b-cert.sngb.ru/am/ipslegals/connect/token";
$clientId = "pos-terminal";

$merchantId = "MB0000021993";           
$account    = "40702810700000107522";   
$redirectUrl = "https://polymedica86.com/success.php";  
$callbackUrl = "https://polymedica86.com/ekassir_callback.php"; 

// ==== ГЕНЕРАЦИЯ JWT ====
$now = time();
$exp = $now + 300;
$claims = [
    "iss" => $issuer,
    "sub" => $subject,
    "aud" => $audience,
    "iat" => $now,
    "exp" => $exp,
    "scope" => "read",
    "jti" => bin2hex(random_bytes(16))
];

$header = ["alg" => "RS256", "typ" => "JWT"];
function base64url_encode($data) {
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

$jwtHeader = base64url_encode(json_encode($header));
$jwtPayload = base64url_encode(json_encode($claims));
$unsignedToken = "$jwtHeader.$jwtPayload";

$privateKey = openssl_pkey_get_private("file://".$privateKeyPath);
if (!$privateKey) die("Ошибка: не удалось загрузить приватный ключ\n");

openssl_sign($unsignedToken, $signature, $privateKey, OPENSSL_ALGO_SHA256);
$jwt = $unsignedToken . "." . base64url_encode($signature);

// ==== ЗАПРОС ACCESS TOKEN ====
$postFields = http_build_query([
    "grant_type" => "urn:ietf:params:oauth:grant-type:jwt-bearer",
    "assertion"  => $jwt,
    "client_id"  => $clientId,
    "scope"      => "read"
]);

$ch = curl_init($audience);
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => $postFields,
    CURLOPT_HTTPHEADER => [
        "Content-Type: application/x-www-form-urlencoded",
        "Accept: application/json"
    ],
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_TIMEOUT => 30
]);
$response = curl_exec($ch);
if (!$response) die("Ошибка запроса токена: " . curl_error($ch));
curl_close($ch);

$data = json_decode($response, true);
if (!isset($data['access_token'])) die("Не удалось получить access token");
$accessToken = $data['access_token'];

// ==== РЕГИСТРАЦИЯ QR-КОДА ====
$baseUrl = "https://c2b-cert.sngb.ru/api/merchant/v1/cash-register-qrc";
//$baseUrl = "https://c2b-cert.sngb.ru/api/merchant/v1/qrc-data?mediaType=image/png&width=300&height=300";

$qrData = [
    "qrcType" => "Dynamic",         // тип QR
    "templateVersion" => "1.0",     // версия шаблона
    "body" => [                      // тело платежа
        "account" => $account,
        "merchantId" => $merchantId,
        "redirectUrl" => $redirectUrl,
        "callbackUrl" => $callbackUrl,
        "amount" => [
            "value" => 100.00,
            "currency" => "RUB"
        ],
        "order" => [
            "orderId" => uniqid("order_"),
            "description" => "Оплата тестовой услуги"
        ]
    ]
];

$ch = curl_init($baseUrl);
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($qrData),
    CURLOPT_HTTPHEADER => [
        "Content-Type: application/json",
        "Authorization: Bearer $accessToken"
    ],
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_TIMEOUT => 30
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

echo "HTTP Code: " . $httpCode . "<br>";
echo "CURL Error: " . $curlError . "<br>";
echo "Response: " . $response . "<br><br>";

if (!$response) die("Не удалось получить QR-код");

$result = json_decode($response, true);
if (!isset($result['image']['content'])) {
    die("Не удалось получить QR-код. Ответ сервера: " . $response);
}

// ==== ОТОБРАЖЕНИЕ QR-КОДА ====
$qrBase64 = $result['image']['content'];
$mimeType = $result['image']['mediaType'] ?? 'image/png';

echo "<h2>Сканируйте QR-код для оплаты:</h2>";
echo "<img src='data:$mimeType;base64,$qrBase64' alt='QR-код'>";
echo "<p>Payload: " . htmlspecialchars($result['payload']) . "</p>";
