<?php
wp_redirect( home_url() . '/appointment' );
get_header(); ?>

<style>
        .schedule {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            max-width: 1100px !important;
            margin: auto;
            font-size: 14px;
        }
        .doc-foto {
            min-width: 200px;
        }
        .doc-foto img{
            width: 200px;
            height: 200px;
        }
        .doc-info {
            margin: 0 10px;
        }
        .schedule-week {
            display: flex;
            flex-direction: column;
            max-width: 400px;
        }
        .schedule__week-title {
            display: flex;
            justify-content: space-evenly;
            
        }

        .schedule__week-day {
            padding:10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            border: 1px solid #ccc;
            font-size: 12px;
            color: ##a3a3a3;
        }
        .schedule__week-day.active {
            color: #000;
            font-weight: bold;
        }

        .schedule__week-day.link {
            color: blue;
            text-decoration: underline;
            cursor: pointer;
        }

        .schedule__buttons {
            display: flex;
            margin: 10px;
            flex-wrap: wrap;
        }
        .schedule__buttons button {
            margin: 2px;
        }

        .schedule__tab {
            display: none;
        }

        .schedule__tab.active {
            display: block;
        }
    </style>

<?php if ( is_home() && ! is_front_page() && ! empty( single_post_title( '', false ) ) ) : ?>
	<header class="page-header alignwide">
		<h1 class="page-title"><?php single_post_title(); ?></h1>
	</header><!-- .page-header -->
<?php endif; ?>
<div class="entry-content" styel="max-width: 1100px">
<?php
$term = get_queried_object();

if ( isset( $term->term_id ) ) {
	echo '<h2>' . $term->name . '</h2>';

	$position_code = (int) $term->description;
	$schedules     = get_schedules( $position_code );

    if ( $schedules ) {
        $postion_name = $term->name;
        $week_days    = get_week_days( $schedules );

        foreach ( $schedules as $schedule ) {
            $doctor              = $schedule[0];
            $doctor_post         = get_doctor_from_code( $doctor['doctor_id'] );
            $max_reception_time  = 15;
            $is_appointment_open = false;

            //echo_var( $doctor_post );

            update_doctor_positions( $doctor_post->ID,  $term->term_id );
            ?>
            <div class="schedule">
            <div class="doc-foto">
                <img src="<?php echo get_doctor_photo( $doctor_post->ID ); ?>" alt="">
            </div>
            <div class="doc-info">
                <h4><?php echo $doctor['fio']; ?> </h4>
                <p><?php echo $postion_name; ?></p>
                <ul>
                    <?php
                    foreach ( $doctor['services'] as $service ) {
                        if ( $service['time'] ) {
                            $parts              = explode( ':', $service['time'] );
                            $service_min        = count( $parts ) === 3 && is_numeric( $parts[1] ) ? (int) $parts[1] : 0;
                            $max_reception_time = max( $max_reception_time, $service_min );
                        }

                        $is_appointment_open = $service['is_first'] ? true : $is_appointment_open;
                        $service_code        = $service['is_first'] ? $service['code'] : $service_code;

                        echo "<li>{$service['name']}  - {$service['price']} руб.</li>";
                    }
                    ?>
                </ul>
            </div>
            <div class="schedule-week">
                <?php
                $schedule_dates = array();

                foreach ( $schedule as $day_schedule ) {

                    $appointment_data = array(
                        'date'         => $day_schedule['date'],
                        'doctor_id'    => $doctor['doctor_id'],
                        'work_place'   => $doctor['work_place'],
                        'service_code' => $service_code,
                    );

                    $day = date( 'd-m', strtotime( $day_schedule['date'] ));

                    $schedule_dates[$day] = array(
                        'date'       => $day_schedule['date'],
                        'start_time' => strtotime( $day_schedule['start_time'] ),
                        'end_time'   => strtotime( $day_schedule['end_time'] ),
                    );
                }
                
                $is_first = true;
                foreach ( $schedule_dates as $day => $times ) {
                    ?>
                    <div class="schedule__tab <?php echo $is_first ? 'active' : ''; ?>" data-date="<?php echo $day; ?>" data-doc="<?php echo $doctor['doctor_id']; ?>">
                        <div class="schedule__week-title">
                            <?php
                            foreach ( $week_days as $week_day_name => $day_date ) {
                                $week_day = date( 'd-m', $day_date );
                                if ( $day === $week_day ) {
                                    echo "<div class=\"schedule__week-day active\"><div>$week_day_name</div><div>$week_day</div></div>";
                                    $is_first = false;
                                    continue;
                                }

                                if (in_array($week_day, array_keys($schedule_dates))) {
                                    echo "<div class=\"schedule__week-day link\" data-doc=\"{$doctor['doctor_id']}\" data-date=\"$week_day\"><div>$week_day_name</div><div>$week_day</div></div>";
                                    continue;
                                }
                                echo "<div class=\"schedule__week-day\"><div>$week_day_name</div><div>$week_day</div></div>";
                            }
                            ?>
                        
                        </div>
                        <div class="schedule__buttons">
                            <?php
                            if ( $is_appointment_open ) {
                                $start_time_timestamp = $times['start_time'];
                                $end_time_timestamp   = $times['end_time'];
                                while ( $start_time_timestamp <= $end_time_timestamp ) {
                                    $appointment_data['date']            = $times['date'];
                                    $appointment_data['start_time']      = date( 'H:i', $start_time_timestamp );
                                    $appointment_data['appoitment_time'] = $max_reception_time; 

                                    $appointment_data_json = wp_json_encode( $appointment_data );

                                    echo '<button class="button" data-info="' . esc_attr( $appointment_data_json ) . '">' . $appointment_data['start_time'] . '</button>';
                                    $start_time_timestamp += $max_reception_time * 60;
                                }
                            }
                            ?>
                        </div>
                    </div>
                <?php
                }
            


            ?>
            </div>
        </div>
        <hr>
            <?php

        }
    }
} else {
	echo 'Нет такой позиции!';
}
?>
</div>

<div id="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); justify-content: center; align-items: center;">
    <div style="background: white; padding: 20px; border-radius: 5px; width: 300px;">
        <h3>Запись на прием</h3>
        <form id="appointmentForm">
            <label for="name">ФИО:</label>
            <input type="text" id="name" name="name" required style="width: 100%; margin-bottom: 10px;">
            
            <label for="phone">Телефон:</label>
            <input type="tel" id="phone" name="phone" required style="width: 100%; margin-bottom: 20px;">
            
            <button type="button" id="submitForm">Записаться</button>
            <button type="button" id="closeModal" style="margin-left: 10px;">X</button>
        </form>
    </div>
</div>

<script>
    const tabs = document.querySelectorAll('.schedule__tab');
    const links = document.querySelectorAll('.schedule__week-day.link');

    links.forEach(link => {
        link.addEventListener('click', () => {
            tabs.forEach(tab => {
                if (tab.dataset.doc === link.dataset.doc ) {
                    if ( tab.dataset.date === link.dataset.date) {
                        tab.classList.add('active');
                    } else {
                        tab.classList.remove('active');
                    }
                }
            });
        });
    })

    
</script>
<?php
get_footer();
