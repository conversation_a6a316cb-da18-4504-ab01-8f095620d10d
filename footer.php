<?php
/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content div and all content after.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package polidemika
 */

?>
	</div>
</div> <!--/.main-container-->
<footer > 
	<div class="container"> 
	<div class="footer_info">
		<div class="logo"><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/logo.svg" style="max-height: 40px"></div>
		<div class="text">Медицинский центр "Полимедика" работает без выходных и стремится предоставить качественные медицинские услуги и своевременную диагностику в удобное для вас время и место.</div>
		<div class="copyright">© 2025 Медицинский центр "Полимедика" Правила использования</div>
	</div>
<?php
if( have_rows('pervoe_menyu_futer', 'option') ): ?>
	<ul class="footer_menu">
    <?php while ( have_rows('pervoe_menyu_futer', 'option') ) : the_row(); ?>
        <li> <a href="<?php the_sub_field('ssylka'); ?>"><?php the_sub_field('punkt'); ?></a></li>
    <?php endwhile; ?>
	</ul>
<?php else :
endif;
?>
<?php
if( have_rows('vtoroe_menyu_futer', 'option') ): ?>
	<ul class="footer_menu">
    <?php while ( have_rows('vtoroe_menyu_futer', 'option') ) : the_row(); ?>
        <li> <a href="<?php the_sub_field('ssylka'); ?>"><?php the_sub_field('punkt'); ?></a></li>
    <?php endwhile; ?>
	</ul>
<?php else :
endif;
?>

	</div>
</footer>

<script>
jQuery(document).ready(function($) {
$('#phone').inputmask("+7 (999) 999-99-99");
});
</script>

<script type="text/javascript" src="//code.jquery.com/jquery-1.11.0.min.js"></script>
<script type="text/javascript" src="//code.jquery.com/jquery-migrate-1.2.1.min.js"></script>
<script type="text/javascript" src="<?php echo get_template_directory_uri(); ?>/js/slick.min.js"></script>

  <script type="text/javascript">
    $(document).ready(function(){
      $('.testimonial').slick({
      infinite: true,
  slidesToShow: 3,
  slidesToScroll: 1,
  dots: true,
  responsive: [
    {
      breakpoint: 620,
      settings: {
        slidesToShow: 1
      }
    }]
      });
    });
    
function slicks() {
    $('.testimonial').slick("refresh");  
}    

  </script>


<?php wp_footer(); ?>


<script type='text/javascript'>
var mycheckbtn = document.getElementsByClassName('mycheck')['0'];
mycheckbtn.onclick = mycheck2;
function mycheck2() {
var submit = document.getElementById('submitForm2');
		console.log(submit);
if (mycheckbtn.checked)
submit.classList.remove('dis');
else
submit.classList.add('dis');
}
	
document.addEventListener('DOMContentLoaded', function() {
    const element = document.querySelector('#pum-2258 .pum-close');
    if (element) {
        element.style.display = 'none';
    }
});	
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        var popupClose = document.querySelector('#pum-2258 .pum-close');
		console.log(popupClose);
        if (popupClose) {
            popupClose.style.display = 'block';
        }
    }, 5000);
});
	
	
let isScrolled = false; // Флаг для отслеживания состояния
let timeout; // Переменная для хранения таймера

window.addEventListener('scroll', function() {
    clearTimeout(timeout); // Сбрасываем предыдущий таймер

    timeout = setTimeout(function() {
        const header = document.getElementById('masthead');
        const scrollPosition = window.scrollY || window.pageYOffset;

        if (scrollPosition > 400 && !isScrolled) {
            header.classList.add('scrolled');
            isScrolled = true; // Устанавливаем флаг, что класс добавлен
        } else if (scrollPosition <= 300 && isScrolled) {
            header.classList.remove('scrolled');
            isScrolled = false; // Сбрасываем флаг, что класс удален
        }
    }, 100); // Задержка в 100 миллисекунд
});
</script>


</body>
</html>