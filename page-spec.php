<?php
/**
 * Template Name: Spec Page
 *
 * @package polidemika
 */

get_header();
?>
<style>
	.team { justify-content: start; }
	.team__card .card-info__position { overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
	
.__select { position: relative; width: 280px; /* height: 40px; */ margin: 0 auto;
	&[data-state="active"] {
	.__select__title { &::before { transform: translate(-3px, -50%) rotate(-45deg); }
						&::after { transform: translate(3px, -50%) rotate(45deg); }
					}
	.__select__content { opacity: 1; }
	/*.__select__label + .__select__input + .__select__label { max-height: 40px; border-top-width: 1px; }*/
	.__select__label { max-height: 40px; border-top-width: 1px; }
																									
		}
.__select__title { display: flex; align-items: center; width: 100%; height: 100%; padding: 8px 24px; border-radius: 12px;cursor: pointer; background: #fff;
	&::before, &::after { content: ""; position: absolute; top: 50%; right: 16px; display: block; width: 10px; height: 2px; transition: all 0.3s ease-out; background-color: rgba(174, 179, 192, 1); transform: translate(-3px, -50%) rotate(45deg); }
		&::after { transform: translate(3px, -50%) rotate(-45deg); }
	&:hover {
		&::before, &::after { background-color: #1062D9; }
		}
	}
.__select__content { position: absolute; top: 40px; left: 3px; display: flex; flex-direction: column; width: calc(100% - 6px); background-color: #ffffff; border-radius: 0 0 12px 12px; transition: all 0.3s ease-out; opacity: 0; z-index: 8; overflow: hidden; }
.__select__input { display: none;
	&:checked + label { background-color: #dedede; }
	&:disabled + label { opacity: 0.6; pointer-events: none; }
}
.__select__label { display: flex; align-items: center; width: 100%; height: 40px; max-height: 0; padding: 0 16px; transition: all 0.2s ease-out; cursor: pointer; overflow: hidden;
	& + input + & { border-top: 0 solid #C7CCD160; }
	&:hover { background-color: #1062D9 !important; color: #ffffff; }
}
</style>
	  
<?php
/**
 * Специалисты на главной
 */


$args = array(
	'post_type'      => 'doctor',
	'orderby'        => 'title',
	'order'          => 'ASC',
	'tax_query'      => array(
		array(
			'taxonomy' => 'podrazdelenie',
			'field'    => 'name',
			'terms'    => '00-000001',
		),
	),
	'posts_per_page' => -1,
);

$doctors = get_posts( $args );

?>

<div class="container mt20" id="specialists"> 
	<div class="block-name">Наша комманда</div>
	<div class="block-f">
		<div class="block-title">Наши медработники</div>
<form style="display: none;">
	<div class="__select" data-state="">
	<div id="__select__title_id" class="__select__title" data-default="Выберите направление">Выберите направление</div>
	<div class="__select__content">
<?php
$categories = get_categories(
	array(
		'taxonomy' => 'position',
		'orderby'  => 'name',
		'order'    => 'ASC',
	)
);
$i == 0;
foreach ( $categories as $category ) {
	++$i;
	?>
	<input id="singleSelect<?php echo $i; ?>" class="__select__input" type="radio" name="singleSelect" />
	<label for="singleSelect<?php echo $i; ?>" class="__select__label" data-catid="<?php echo $category->term_id; ?>"><?php echo $category->name; ?></label>
<?php } ?>
	</div>
	</div>
</form>	
		

		<div class="ggg" id="appointment">
			<div class="f-between f-align-end">
				<div class="appointment__block">
          <select name="position" class="open_one" id="position_select" style="display: none; color: gray;" disabled>
            <option selected value="">Выберите направление</option>
<?php
$categories = get_categories(
	array(
		'taxonomy' => 'position',
		'orderby'  => 'name',
		'order'    => 'ASC',
	)
);
$i == 0;
foreach ( $categories as $category ) {
	++$i;
	?>
	<option value="<?php echo $category->term_id; ?>" style="color: black;" data-children=""><?php echo $category->name; ?></option>
<?php } ?>
			  
			  
            <?php /*
            $positions = get_all_positions();
            foreach ( $positions as $position ) {
              if ( ! in_array( $position->term_id, array( 63 ) ) ) { // Скрываем ненужные направления
                $linked_categories = get_field( 'linked_categories', $position );
                $is_children       = in_array( 60, $linked_categories );
                echo '<option value="' . (int) $position->description . '" style="color: black;" data-children="' . ( $is_children ? 'true' : 'false' ) . '">' . $position->name . '</option>';
              }
            }
           */ ?>
            </select>
				</div>
			</div>
		</div>
		
		
	</div>
	<div class="team" style="display: none;">
		<?php foreach ( $doctors as $doctor ) : ?>
			<div class="team__card" data-term="<?php echo get_first_doctor_position( $doctor->ID )->term_id; ?>">
				<div class="card-foto" style="position: relative;">
<?php if (get_field('stazh', $doctor->ID)) { ?>
					<div class="staje" style="position: absolute; top: 0; right: 13px; width: 71px; height: 78px; box-sizing: border-box; padding: 6px 0 10px; background: #fff; box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.05); display: flex; flex-direction: column; align-items: center; border-radius: 0 0 8px 8px;">
						<img style="width: 24px; height: 24px; margin-bottom: 8px;" src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/staje.svg">
						<div style="text-align: center; line-height: 1.2">
						<span style="color: rgba(174, 179, 192, 1)">опыт</span><br>
<?php  
$originalDate = get_field('stazh', $doctor->ID);
$dateObj = DateTime::createFromFormat('d/m/Y', $originalDate);
$newDate = $dateObj->format('Y-m-d');
			
$startDate = new DateTime($newDate);
$currentDate = new DateTime();

$interval = $currentDate->diff($startDate);

$years = $interval->y;
$months = $interval->m;

echo plural($years, ['год', 'года', 'лет']);
?>
						</div>
					</div>
<?php } ?>
					<img src="<?php echo get_doctor_photo( $doctor->ID ); ?>">
				</div>
				<div class="card-info"> 
					<div class="card-info__text">
					<div class="card-info__fio">
						<?php echo get_doctor_fio( $doctor->ID ); ?>
					</div>
					<div class="card-info__rating">
					<div class="stars"> </div>
					<div class="points"><?php echo ( get_field( 'score', $doctor->ID ) ) ? get_field( 'score', $doctor->ID ) : '0'; ?></div>
					</div>
					</div>
					<div class="card-info__position">
						<?php
							$position = get_first_doctor_position( $doctor->ID );
						if ( $position ) {
							echo $position->name;
						}
						?>
						
					</div>
				</div>
				<div class="card-buttons">
					<?php
						$hashItems = array();
						// Код направления
					if ( $doctor_position = term_description( $position ) ) {
						$doctor_position = strip_tags( $doctor_position );
						$hashItems[]     = "p:{$doctor_position}";
					}
						// Код врача
					if ( $doctor_code = get_post_meta( $doctor->ID, 'code', true ) ) {
						$doctor_code = str_pad( $doctor_code, 11, '0', STR_PAD_LEFT );
						$hashItems[] = "d:{$doctor_code}";
					}
					?>
					<a href="<?php echo home_url(); ?>/appointment/#<?php echo implode( '_', $hashItems ); ?>">
						<button><img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/appointment-blue.svg" alt="">Записаться на прием</button>
					</a>
					<a class="card-link" href="<?php echo $doctor->guid; ?>">Подробнее</a>
				</div>
			</div>
		<?php endforeach; ?>
		
	</div>
<script type="text/javascript">
/*
const selectSingle = document.querySelector('.__select');
const selectSingle_title = selectSingle.querySelector('.__select__title');


selectSingle_title.addEventListener('click', () => {
	if ('active' === selectSingle.getAttribute('data-state')) {
		selectSingle.setAttribute('data-state', '');
	} else {
		selectSingle.setAttribute('data-state', 'active');
	}
});
*/
	window.onload = function() {
/*
const selectSingle_labels = document.querySelectorAll('.custom-select__option');
console.log(selectSingle_labels);
		
const hashID = (window.location.hash.match(/^#(\d+)$/) ?? [,''])[1];
var elementss = document.getElementsByClassName('team__card');
for (let i = 0; i < selectSingle_labels.length; i++) {
	let catID = selectSingle_labels[i].getAttribute('data-value');
	selectSingle_labels[i].addEventListener('click', (evt) => {
		console.log("!!!");
		selectSingle_title.textContent = evt.target.textContent;
		for (let elem of elementss) {
			elem.style.display = "flex";
			if (elem.getAttribute('data-term') != catID) {
				elem.style.display = "none";
			}	
		}
		window.location.hash = '#' + catID;
		selectSingle.setAttribute('data-state', '');
	});
	if(hashID == catID) {
		selectSingle_labels[i].click();
	}
}
*/
document.querySelector('.team').style.display = "flex";
		
	};
</script>
</div>
<? wp_reset_postdata(); ?>
	  

	</div>
<?php
get_footer();
