<?php
/**
 * polidemika functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package polidemika
 */

if ( ! defined( '_S_VERSION' ) ) {
	define( '_S_VERSION', '1.0.10' );
}

require_once get_template_directory() . '/func/functions-post-types.php';
require_once get_template_directory() . '/func/functions-config.php';
require_once get_template_directory() . '/func/functions-api.php';
require_once get_template_directory() . '/func/functions-get.php';
require_once get_template_directory() . '/func/functions-jobtitle.php'; // страница должностей в админке
require_once get_template_directory() . '/func/functions-ekassir.php'; // страница должностей в админке

function polidemika_setup() {
	add_theme_support( 'title-tag' );
	add_theme_support( 'post-thumbnails', array( 'post', 'page', 'awards', 'doctor' ) );
	add_image_size( 'awards-size', 380, 280, true );


	register_nav_menus(
		array(
			'menu-1' => esc_html__( 'Primary', 'polidemika' ),
		)
	);


	add_theme_support(
		'html5',
		array(
			'search-form',
			'comment-form',
			'comment-list',
			'gallery',
			'caption',
			'style',
			'script',
		)
	);

	// Set up the WordPress core custom background feature.
	add_theme_support(
		'custom-background',
		apply_filters(
			'polidemika_custom_background_args',
			array(
				'default-color' => 'ffffff',
				'default-image' => '',
			)
		)
	);

	// Add theme support for selective refresh for widgets.
	add_theme_support( 'customize-selective-refresh-widgets' );

}
add_action( 'after_setup_theme', 'polidemika_setup' );

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 *
 * Priority 0 to make it available to lower priority callbacks.
 *
 * @global int $content_width
 */
function polidemika_content_width() {
	$GLOBALS['content_width'] = apply_filters( 'polidemika_content_width', 640 );
}
add_action( 'after_setup_theme', 'polidemika_content_width', 0 );


/**
 * Enqueue scripts and styles.
 */
function polidemika_scripts() {
    // Подключаем основной стиль
    wp_enqueue_style( 'polidemika-style', get_template_directory_uri() . '/assets/css/main.css', array(), _S_VERSION );
    wp_style_add_data( 'polidemika-style', 'rtl', 'replace' );

    // Подключаем скрипт навигации
    wp_enqueue_script( 'polidemika-navigation', get_template_directory_uri() . '/js/navigation.js', array(), _S_VERSION, true );

    // Подключаем jQuery UI (datepicker)
    wp_enqueue_script( 'jquery-ui-datepicker' );

    // Подключаем стиль jQuery UI Datepicker
    wp_enqueue_style(
        'jquery-ui-datepicker-style',
        'https://code.jquery.com/ui/1.13.2/themes/smoothness/jquery-ui.css'
    );

    // Подключаем основной скрипт после jQuery и jQuery UI
    wp_enqueue_script( 'polidemika-main-script', get_template_directory_uri() . '/assets/js/scripts.js', array('jquery', 'jquery-ui-datepicker'), _S_VERSION, true );

    // Локализуем скрипт для использования AJAX
    wp_localize_script( 'polidemika-main-script', 'ajax_object', array( 'ajax_url' => admin_url( 'admin-ajax.php' ) ) );

    // Подключаем скрипт для комментариев на страницах с открытыми комментариями
    if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
        wp_enqueue_script( 'comment-reply' );
    }
}
add_action( 'wp_enqueue_scripts', 'polidemika_scripts' );


/**
 * Implement the Custom Header feature.
 */
require get_template_directory() . '/inc/custom-header.php';

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
require get_template_directory() . '/inc/template-functions.php';

/**
 * Customizer additions.
 */
require get_template_directory() . '/inc/customizer.php';

/**
 * Load Jetpack compatibility file.
 */
if ( defined( 'JETPACK__VERSION' ) ) {
	require get_template_directory() . '/inc/jetpack.php';
}



// function enqueue_datepicker_assets() {
//     // Подключаем jQuery UI
//     wp_enqueue_script( 'jquery-ui-datepicker' );

//     // Подключаем стиль jQuery UI (вы можете использовать любой другой стиль, если хотите)
//     wp_enqueue_style(
//         'jquery-ui-datepicker-style',
//         'https://code.jquery.com/ui/1.13.2/themes/smoothness/jquery-ui.css'
//     );

//     // Подключаем ваш скрипт для инициализации календаря
//     // wp_enqueue_script(
//     //     'custom-datepicker',
//     //     get_template_directory_uri() . '/assets/js/custom-datepicker.js', // Путь к вашему JS-файлу
//     //     array( 'jquery', 'jquery-ui-datepicker' ),
//     //     false,
//     //     true
//     // );
// }
// add_action( 'wp_enqueue_scripts', 'enqueue_datepicker_assets' );
// 
// 
add_filter( 'excerpt_more', 'new_excerpt_more' );
function new_excerpt_more( $more ){
    global $post;
    return '<a class="read_more" href="'. get_permalink($post) . '">Читать полностью ></a>';
}

if( function_exists('acf_add_options_page') ) {	acf_add_options_page(); }




function plural($number, $titles) {
    $cases = array(2, 0, 1, 1, 1, 2);
    return $number . " " . $titles[($number % 100 > 4 && $number % 100 < 20) ? 2 : $cases[min($number % 10, 5)]];
}


add_action('wp_footer', 'add_cookie_notice');

function add_cookie_notice() {
    ?>
    <div id="cookie-notice" style="display:none; position: fixed; bottom: 0; left: 0; width: 100%; background-color: #f0f0f0; padding: 10px; text-align: center; border-top: 1px solid #ccc;">
        <p>Продолжая использовать polymedica86.com, вы соглашаетесь с <a href="https://polymedica86.com/privacy-policy/">политикой конфиденциальности</a> и использованием файлов cookie. <a style="color: #007bff;cursor: pointer; text-decoration: underline;" onclick="acceptCookies()">Принять</a>
           </p>
        
    </div>
    <script>
        function acceptCookies() {
            document.getElementById('cookie-notice').style.display = 'none';
            document.cookie = "cookie_consent=true; path=/";
        }
        if (document.cookie.indexOf('cookie_consent=true') === -1) {
            document.getElementById('cookie-notice').style.display = 'block';
        }
    </script>
    <?php
}