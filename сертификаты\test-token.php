<?php
// test-ekassir.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

$privateKeyPath = __DIR__ . '/certs/LB0000011859-ips-sysuser-staging-client.key';

$subject = "ac-248862766";

$issuer = "https://polymedica86.com/";

$audience = "https://c2b-cert.sngb.ru/am/ipslegals/connect/token";

$clientId = "pos-terminal";

// ==== ФОРМИРУЕМ JWT ====

// Время жизни токена (например, 5 минут)
$now = time();
$exp = $now + 300;

$claims = [
    "iss" => $issuer,
    "sub" => $subject,
    "aud" => $audience,
    "iat" => $now,
    "exp" => $exp,
    "scope" => "read",
    "jti" => bin2hex(random_bytes(16))
];

echo '<pre>';
echo json_encode($claims, JSON_PRETTY_PRINT);
echo '</pre>';

$header = [
    "alg" => "RS256",
    "typ" => "JWT"
];

function base64url_encode($data) {
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

$jwtHeader = base64url_encode(json_encode($header));
$jwtPayload = base64url_encode(json_encode($claims));
$unsignedToken = "$jwtHeader.$jwtPayload";

// Подпись
$privateKey = openssl_pkey_get_private("file://".$privateKeyPath);
echo $privateKeyPath;
if (!$privateKey) {    
    die("Ошибка: не удалось загрузить приватный ключ\n");

}

openssl_sign($unsignedToken, $signature, $privateKey, OPENSSL_ALGO_SHA256);
$jwtSignature = base64url_encode($signature);

$jwt = "$unsignedToken.$jwtSignature";


$postFields = http_build_query([
    "grant_type" => "urn:ietf:params:oauth:grant-type:jwt-bearer",
    "assertion"  => $jwt,
    "client_id"  => $clientId,
    "scope"      => "read"
]);

echo '<h3>Запрос в формате документации:</h3>';
echo '<pre>';
echo "POST " . $audience . " HTTP/1.1\n";
echo "Host: " . parse_url($audience, PHP_URL_HOST) . "\n";
echo "Accept: application/json\n";
echo "X-Requested-With: XMLHttpRequest\n";
echo "X-Correlation-Id: CheckAuth\n";
echo "Request-Timeout: 30\n";
echo "Connection: keep-alive\n";
echo "Content-Type: application/x-www-form-urlencoded\n";
echo "Content-Length: " . strlen($postFields) . "\n";
echo "\n";
echo $postFields;
echo '</pre>';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $audience);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    "Content-Type: application/x-www-form-urlencoded",
    "Accept: application/json",
    "X-Requested-With: XMLHttpRequest",
    "X-Correlation-Id: CheckAuth",
    "Request-Timeout: 30",
    "Connection: keep-alive"
]);

// отладка перед CURL запросом
echo '<h3>Отладочная информация:</h3>';
echo 'URL: ' . $audience . '<br>';
echo 'JWT Header: ' . $jwtHeader . '<br>';
echo 'JWT Payload: ' . $jwtPayload . '<br>';
echo 'JWT Signature: ' . $jwtSignature . '<br>';
echo 'Полный JWT: ' . $jwt . '<br><br>';


curl_setopt($ch, CURLOPT_TIMEOUT, 60);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);

echo '<h3>Результат запроса:</h3>';
echo 'HTTP Code: ' . $httpCode . '<br>';

curl_close($ch);

header('Content-Type: application/json');

echo "---------------------------------------<br>";

$data = json_decode($response, true);
$accessToken = $data['access_token'];
echo "Access Token: " . $accessToken . "<br>";

$baseUrl = "https://c2b-cert.sngb.ru/api"; // тестовый стенд
$callbackUrl = "https://polymedica86.com//api/ekassir/callback"; // URL для приёма уведомлений от банка

// Данные платежа
$data = [
    "amount" => [
        "currency" => "RUB",
        "value" => 100.00  // сумма к оплате
    ],
    "order" => [
        "orderId" => uniqid("order_"), // уникальный ID заказа
        "description" => "Оплата тестовой услуги"
    ],
    "customer" => [
        "phone" => "+79990000000",
        "email" => "<EMAIL>"
    ],
    "redirectUrl" => "https://polymedica86.com/success.php", // куда вернуть клиента после оплаты
    "callbackUrl" => $callbackUrl // уведомление от банка
];

// Запрос
$ch = curl_init("$baseUrl/v2/payment-links");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    "Authorization: Bearer $accessToken",
    "Content-Type: application/json"
]);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);

echo "<h3>HTTP-код ответа:</h3> $httpCode<br>";

if ($response === false) {
    echo "<h3>Ошибка CURL:</h3> $curlError<br>";
} else {
    echo "<h3>Сырой ответ сервера:</h3><pre>";
    echo htmlspecialchars($response);
    echo "</pre>";

    $result = json_decode($response, true);
    echo "<h3>JSON декодированный:</h3><pre>";
    print_r($result);
    echo "</pre>";

    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "<strong>Ошибка JSON:</strong> " . json_last_error_msg() . "<br>";
    }

    if (!empty($result['paymentLink'])) {
        echo "<a href='{$result['paymentLink']}' target='_blank'>Перейти к оплате</a>";
    }
}

curl_close($ch);
