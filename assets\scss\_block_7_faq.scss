.faq {
    margin-top: 40px;
    margin-bottom: 80px;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
    max-width: 720px;
    
    
    &__card {
        border: 1px solid #AEB3C0;
        border-radius: 12px;
        padding: 20px;
        width: 100%;

        .question {
            @extend %font_600;
            font-size: 18px;
            line-height: 1.2;
        }

        .arrow {
            width: 20px;
            height: 20px;
            background-image: url(../images/arrow-down.svg);
            background-repeat: no-repeat;
            background-position: center;
            cursor: pointer;
            outline: none; /* Убирает рамку при фокусировке */
            user-select: none; 
        }

        .answer {
            display: none;
            margin-top: 20px;
            @extend %font_500;
            font-size: 16px;
            line-height: 1.2;
        }
    }

    &__card.active {
        background-color: #fff;
        border: none;

        .arrow {
            transform: rotate(180deg);
        }
        .answer {
            display: block;
        }
    }

}

.f-between {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}