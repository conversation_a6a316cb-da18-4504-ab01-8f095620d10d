<?php
/**
 * Награды
 */
$args = array(
    'post_type'      => 'awards',
    'posts_per_page' => 6, 
);


$query = new WP_Query($args);


?>

<div class="container mt20"> 
    <div class="block-name">Гарантии качества</div>
    <div class="block-f">
        <div class="block-title">Подтверждение компетентности</div>
        <div class="block-link"><a href="#">Показать все</a></div>
    </div>
    <div class="diploms"> 
        <?php 
        while ($query->have_posts()) : $query->the_post();
            $full_image_url = wp_get_attachment_url(get_post_thumbnail_id());
        ?>
            <div class="diploms__image"> 
                <a href="<?php echo esc_url($full_image_url); ?>" data-lightbox="diplom-gallery">
                    <?php //the_post_thumbnail( 'awards-size' );  ?>
					<?php the_post_thumbnail( 'large' );  ?>
                </a>

            </div>
        <?php endwhile; ?>
    </div>
</div>

<?php wp_reset_postdata(); ?>