<?php
/**
 * Отзывы на главной
 */

 $args = array(
    'post_type'      => 'reviews',
    'posts_per_page' => 6, 
);


$reviews= get_posts( $args );
?>
<div class="container mt20"> 
    <div class="block-name">Отзывы</div>
    <div class="block-f">
        <div class="block-title">Нам доверяют</div>
        <!--div class="block-link">
        <button class="white left-arrow"></button>
        <button class="white right-arrow"></button>
        </div-->
    </div>
    <div class="testimonial">
        <?php foreach ( $reviews as $review ) : ?>
            <div class="testimonial__card">
                <div class="t-card__top">
                    <div class="t-card__person">
                    <div class="t-card__photo">
                        <?php $img_src = esc_url( get_field( 'review_foto', $review->ID ) ); ?>
                        <img src="<?php echo $img_src; ?>">
                    </div>
                    <div class="t-card__title">
                        <div class="t-card__name">
                            <?php echo get_field( 'review_name', $review->ID ); ?>
                        </div>
                        <div class="t-card__date">
                        <?php echo get_field( 'review_date', $review->ID ); ?>
                        </div>
                    </div>
                    </div>
                    <div class="t-card__rating"> 
                        <div class="t-card__logo">
                            <img src="<?php echo get_field( 'review_logo', $review->ID ); ?>">
                        </div>
                        <div class="t-card__points">
                            <?php echo get_field( 'review_score', $review->ID ); ?>
                        </div>
                    </div>
                </div>
                <div class="t-card__text"><?php echo get_field( 'review_text', $review->ID ); ?></div>
                </div>
        <?php endforeach; ?>
    <!--div class="testimonial-nav"> 
        <div class="testimonial-nav__gray"> </div>
        <div class="testimonial-nav__blue"></div>
        <div class="testimonial-nav__gray"> </div>
    </div-->
</div>

<?php wp_reset_postdata(); ?>