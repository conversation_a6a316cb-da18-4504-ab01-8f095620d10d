<?php
defined( 'ABSPATH' ) || die();


class EkassirApiClient {
    private $client_id = 'pos-terminal';
    private $scope = 'read';
    private $token_url = 'https://c2b-cert.sngb.ru/am/ipslegals/connect/token';
    private $private_key_path = ABSPATH  . '/certs/LB0000011859-ips-sysuser-staging-client.key'; // путь к приватному ключу
    private $iss = 'https://polymedica86.com/';                // как у тебя в claims
    private $sub = 'ac-248862766';                        // системный логин
    private $aud = 'https://c2b-cert.sngb.ru/am/ipslegals/connect/token';

    /**
     * Получить токен (из кэша или заново)
     */
    public function get_token() {
        $token_data = get_transient('ekassir_access_token');
        if ($token_data && isset($token_data['access_token'], $token_data['expires_at'])) {
            if (time() < $token_data['expires_at']) {
                return $token_data['access_token'];
            }
        }
        return $this->request_token();
    }

    /**
     * Запросить новый токен
     */
    private function request_token() {
        $iat = time();
        $exp = $iat + 300; // живёт 5 минут (Access Manager сам ограничит)

        $claims = [
            'iss'  => $this->iss,
            'sub'  => $this->sub,
            'aud'  => $this->aud,
            'iat'  => $iat,
            'exp'  => $exp,
            'scope'=> $this->scope,
            'jti'  => bin2hex(random_bytes(16)),
        ];

        // создаем JWT
        $jwt = $this->generate_jwt($claims);

        // готовим POST
        $response = wp_remote_post($this->token_url, [
            'timeout' => 30,
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'body' => [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion'  => $jwt,
                'client_id'  => $this->client_id,
                'scope'      => $this->scope,
            ],
        ]);

        if (is_wp_error($response)) {
            throw new Exception('Token request failed: ' . $response->get_error_message());
        }

        $data = json_decode(wp_remote_retrieve_body($response), true);

        if (!isset($data['access_token'])) {
            throw new Exception('No access token in response: ' . wp_remote_retrieve_body($response));
        }

        // сохраняем с запасом (expires_in обычно 3600)
        $expires_at = time() + intval($data['expires_in'] ?? 3600) - 30;
        set_transient('ekassir_access_token', [
            'access_token' => $data['access_token'],
            'expires_at'   => $expires_at,
        ], $data['expires_in'] ?? 3600);

        return $data['access_token'];
    }

    /**
     * Сформировать JWT и подписать приватным ключом
     */
    private function generate_jwt(array $claims) {
        $header = ['alg' => 'RS256', 'typ' => 'JWT'];
        $segments = [];
        $segments[] = $this->urlsafeB64(json_encode($header));
        $segments[] = $this->urlsafeB64(json_encode($claims));

        $signing_input = implode('.', $segments);
        $signature = '';
        $privateKey = file_get_contents($this->private_key_path);
        $key = openssl_pkey_get_private($privateKey);

        if (!$key) {
            throw new Exception('Cannot load private key');
        }

        openssl_sign($signing_input, $signature, $key, OPENSSL_ALGO_SHA256);
        openssl_free_key($key);

        $segments[] = $this->urlsafeB64($signature);
        return implode('.', $segments);
    }

    private function urlsafeB64($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * Универсальный запрос к API
     */
    public function request($endpoint, $body = [], $method = 'POST') {
        $token = $this->get_token();
        $url = 'https://c2b-cert.sngb.ru/api/' . ltrim($endpoint, '/');

        $args = [
            'method'  => $method,
            'timeout' => 30,
            'headers' => [
                'Authorization' => 'Bearer ' . $token,
                'Content-Type'  => 'application/json',
            ],
        ];

        if (!empty($body)) {
            $args['body'] = json_encode($body, JSON_UNESCAPED_UNICODE);
        }

        $response = wp_remote_request($url, $args);

        if (is_wp_error($response)) {
            throw new Exception('API request failed: ' . $response->get_error_message());
        }

        return json_decode(wp_remote_retrieve_body($response), true);
    }

    /**
     * Создать QR-код для оплаты
     */
    public function create_qr_code($amount, $order_id, $description = '', $redirect_url = '', $callback_url = '') {
        $merchant_id = "MB0000021993";
        $account = "40702810700000107522";

        $qr_data = [
            "account" => $account,
            "merchantId" => $merchant_id,
            "templateVersion" => "01",
            "qrcType" => "02",
            "amount" => $amount, 
            "currency" => "RUB",
            "qrcTtl" => 600,
            "paymentPurpose" => "Оплата " . $description,
            "params" => [
                "paymentData" => "order_id:" . $order_id
            ]
        ];
        
        return $this->request('/merchant/v1/qrc-data?mediaType=image/png&width=300&height=300', $qr_data);
    }

    // Эмуляция оплаты на тестовом кассире
    public function emulate_payment($qrcId, $payload = '') {
        $urls = [
            "https://connect--sandbox.sngb.ru/ips/c2b/api/selfcare/v1/{$qrcId}/pay",
            "https://connect-sandbox.sngb.ru/ips/c2b/api/selfcare/v1/{$qrcId}/pay",
            "https://sandbox.sngb.ru/ips/c2b/api/selfcare/v1/{$qrcId}/pay",
            "https://c2b-cert.sngb.ru/api/selfcare/v1/{$qrcId}/pay"
        ];
        
        $body = [];
        if (!empty($payload)) {
            $body['payload'] = $payload;
        }
        
        foreach ($urls as $url) {
            $args = [
                'method'  => 'POST',
                'timeout' => 30,
                'headers' => [
                    'Content-Type'  => 'application/json',
                ],
                'body' => json_encode($body),
                'sslverify' => false,
            ];

            $response = wp_remote_request($url, $args);
            
            if (!is_wp_error($response)) {
                $http_code = wp_remote_retrieve_response_code($response);
                $response_body = wp_remote_retrieve_body($response);
                
                consolelog("URL: $url, HTTP: $http_code, Body: $response_body");
                
                // Возвращаем результат первого успешного запроса
                return [
                    'url' => $url,
                    'http_code' => $http_code,
                    'response' => json_decode($response_body, true) ?: $response_body
                ];
            }
            
            consolelog("Failed URL: $url - " . $response->get_error_message());
        }

        return ['error' => 'All sandbox URLs failed'];
    }
}

// Шорткод для теста API
add_shortcode('ekassir_test', function () {
    ob_start();

    try {
        $api = new EkassirApiClient();
        $result = $api->request('members/v1/banks', [], 'GET'); // пример: список банков СБП
        echo '<h3>Результат запроса:</h3>';
        echo '<pre>' . esc_html(print_r($result, true)) . '</pre>';
    } catch (Exception $e) {
        echo '<strong>Ошибка:</strong> ' . esc_html($e->getMessage());
    }

    return ob_get_clean();
});

// Регистрируем REST-эндпоинт для обработки уведомлений
add_action('rest_api_init', function () {
    register_rest_route('ekassir/v1', '/callback', [
        'methods'             => 'POST',
        'callback'            => 'ekassir_callback_handler',
        'permission_callback' => '__return_true',
    ]);
});

function ekassir_callback_handler(WP_REST_Request $request) {
    $raw = $request->get_body(); // JWS строка
    $logFile = WP_CONTENT_DIR . '/uploads/ekassir_callback_log.txt';

    file_put_contents($logFile, date('Y-m-d H:i:s') . " RAW: $raw\n", FILE_APPEND);

    $parts = explode('.', $raw);
    if (count($parts) !== 3) {
        return new WP_REST_Response(['error' => 'Invalid JWS format'], 400);
    }

    // Payload — это вторая часть
    $payload = json_decode(base64url_decode($parts[1]), true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        return new WP_REST_Response(['error' => 'Invalid payload JSON'], 400);
    }

    // Логируем полезную нагрузку
    file_put_contents($logFile, "Payload: " . print_r($payload, true) . "\n", FILE_APPEND);

    // Пример разбора
    $code   = $payload['code'] ?? '';
    $status = $payload['status'] ?? '';
    $qrcId  = $payload['qrcId'] ?? '';

    // TODO: тут ты можешь обновить заказ, запись в БД и т.д.
    // например: если status == 'ACWP', значит привязка прошла успешно

    return new WP_REST_Response(['result' => 'OK'], 200);
}

function base64url_decode($data) {
    $remainder = strlen($data) % 4;
    if ($remainder) {
        $padlen = 4 - $remainder;
        $data .= str_repeat('=', $padlen);
    }
    return base64_decode(strtr($data, '-_', '+/'));
}

// Функция для конвертации цены в копейки
function price_to_int_kopeiki($price_string) {
    $clean_price = preg_replace('/[^\d,\.]/', '', $price_string);
    $rubles = floatval(str_replace(',', '.', $clean_price));
    return round($rubles * 100);
}

