<style>
.mssg { padding: 40px; font-size: 18px; text-align: center; background: #fff; margin: 0 auto; border-radius: 10px; }
</style>
<?php
/* Template Name: Sended Page */


if ( $_SERVER['REQUEST_METHOD'] === 'POST' ) {
	// Получаем данные из формы
	$lastName   = $_POST['last_name'] ?? '';  // Фамилия
	$firstName  = $_POST['first_name'] ?? ''; // Имя
	$middleName = $_POST['middle_name'] ?? ''; // Отчество
	$phone      = $_POST['phone'] ?? '';  // Телефон
	$phone = preg_replace('/[^0-9]/', '', $phone);

	// Объединяем Фамилию, Имя и Отчество в одну строку
	$fullName = trim( $lastName . ' ' . $firstName . ' ' . $middleName );

	// Дополнительные данные
	//$birthDate = $_POST['birthDate'] ?? '';
	$doctorId        = $_POST['doctorId'] ?? '';
	$serviceCode     = $_POST['serviceCode'] ?? '';
	$serviceName     = $_POST['serviceName'] ?? '';
	$servicePriceStr = $_POST['servicePrice'] ?? '';
	$workPlace       = $_POST['work_place'] ?? '';
	$date            = $_POST['date'] ?? '';
	$time            = $_POST['time'] ?? '';

	// получаем цену в копейках
	$servicePrice = price_to_int_kopeiki( $servicePriceStr );

	// Форматирование даты для записи
	$datetime       = DateTime::createFromFormat( 'd.m.Y H:i', $date . ' ' . $time );
	$formatted_date = $datetime->format( 'YmdHis' );

	// клиент ребенок ?
	$is_child = false;

	$birth_date = DateTime::createFromFormat( 'Y-m-d', $_POST['birthDate'] );
	if ( $birth_date ) {
		$age_difference = ( new DateTime() )->diff( $birth_date );
		$is_child       = $age_difference->y < 16;
	}

	$formatted_birth_date = $birth_date->format( 'Ymd' );

	// Данные для записи
	$appointment_data = array(
		'КодПодразделения' => KOD_PODRAZDEL,
		'Дата'             => $formatted_date,
		'КодСотрудника'    => $doctorId,
		'КодРабочееМесто'  => $workPlace,
		'КодНоменклатуры'  => $serviceCode,
		'ФИО'              => $fullName,
		'Телефон'          => $phone,
		'Ребенок'          => $is_child,
		'ДатаРождения'     => $formatted_birth_date,
	);
	
	$my_formatted_date = $datetime->format( 'd.m.Y в H:i' );
	$doctor   = get_doctor_from_code( $doctorId );
	$position = get_first_doctor_position( $doctor->ID );
	$doc_fio  = get_field( 'fio', $doctor->ID );

	// Получаем URL для записи 
	$url = get_appointment_url( $appointment_data );

	//Выполняем запрос к серверу
	 if ( current_user_can( 'administrator' ) ) {
        // Для админа - имитируем успешный ответ
        $response = array(
            'response' => array( 'code' => 200 ),
            'body' => 'Результат="Успех"'
        );
		$message = '
				<style>
					.talon { max-width: 900px; margin: 0 auto; padding: 60px; border-radius: 12px; background: #fff; }
					.talon_title { font-family: Montserrat; font-weight: 800; font-size: 32px; line-height: 39.01px; letter-spacing: 0px; text-align: center;  color: #011424; margin-bottom: 40px; }
					.talon_wrap { display: grid; gap: 40px; grid-template-columns: 3fr 4fr; }
					.talon_img { aspect-ratio: 1 / 1; border-radius: 12px; overflow: hidden; }
					.talon_img img { width: 100%; height: 100%; object-fit: cover; }
					.talon_info { display: flex; flex-direction: column; gap: 20px; }
					.tal_info_row span { color: #AEB3C0; }
					@media screen and (max-width: 620px) {
						.talon_wrap { grid-template-columns: 1fr; }
						.talon_img { aspect-ratio: 4 / 3; }
					}
				</style>
				<div class="talon">
					<div class="talon_title">Талон записи</div>
					<div class="talon_wrap">
						<div class="talon_img"><img src="' . get_doctor_photo( $doctor->ID ) . '"></div>
						<div class="talon_info">
							<div class="tal_info_row">Вы записаны<br> в МЦ ООО &quot;ПОЛИМЕДИКА&quot;</div>
							<div class="tal_info_row"><span>На дату:</span><br>' . $my_formatted_date . '</div>
							<div class="tal_info_row"><span>К доктору:</span><br>' . $position->name . '<br>' . $doc_fio . '</div>
							<div class="tal_info_row"><span>На прием/процедуру:</span><br>' . $serviceName . '</div>
						</div>
				';
				// если $serviceName содержит слово "первичный"

				if ( strpos( $serviceName, 'первичный' ) !== false && $servicePrice > 0 ) {
					$message .= '<h5>Вы можете оплатить услугу по QR-коду или при посещении поликлиники.</h5>';
					$api = new EkassirApiClient();
					$order_id = 'appt_' . $doctorId . '_' . date('YmdHis');
					$result = $api->create_qr_code($servicePrice, $order_id, $serviceName);
					$qrcId = $result['qrcId'] ?? '';
					consolelog("QR created at: " . date('Y-m-d H:i:s') . " for qrcId: " . ($result['qrcId'] ?? 'none'));
					if (isset($result['image']['content'])) {
                        $qrBase64 = $result['image']['content'];
                        $mimeType = $result['image']['mediaType'] ?? 'image/png';
                        $payload = $result['payload'] ?? '';
                        
                        $message .= '<div style="text-align: center; margin: 20px 0;">';
                        $message .= '<img src="data:' . $mimeType . ';base64,' . $qrBase64 . '" alt="QR-код для оплаты" style="max-width: 300px;">';
                        $message .= '<br><a href="' . $payload . '" target="_blank" style="display: inline-block; margin-top: 10px; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 5px;">Оплатить по ссылке</a>';
                        $message .= '</div>';
                    }

					if ($qrcId && current_user_can('administrator')) {
						consolelog("Testing payment at: " . date('Y-m-d H:i:s'));
						$payment_result = $api->emulate_payment($qrcId, $payload);
						consolelog('Test payment result: ' . print_r($payment_result, true));
					}
				}

				$message .= '
					</div>
				</div>';
    } else {
		$response = server_remote_get( $url );
		log_remote_request( $appointment_data, $response );

		// Обрабатываем ответ
		if ( wp_remote_retrieve_response_code( $response ) == 200 ) {
			$body = wp_remote_retrieve_body( $response ); // Получаем тело ответа

			if ( strpos( $body, 'Результат="Занято"' ) !== false ) {
				$message = '<div class="mssg">Время уже занято.</div>';
			}
			else {
				//$message = 'Запись успешно сделана.';
				$message = '
				<style>
					.talon { max-width: 900px; margin: 0 auto; padding: 60px; border-radius: 12px; background: #fff; }
					.talon_title { font-family: Montserrat; font-weight: 800; font-size: 32px; line-height: 39.01px; letter-spacing: 0px; text-align: center;  color: #011424; margin-bottom: 40px; }
					.talon_wrap { display: grid; gap: 40px; grid-template-columns: 3fr 4fr; }
					.talon_img { aspect-ratio: 1 / 1; border-radius: 12px; overflow: hidden; }
					.talon_img img { width: 100%; height: 100%; object-fit: cover; }
					.talon_info { display: flex; flex-direction: column; gap: 20px; }
					.tal_info_row span { color: #AEB3C0; }
					@media screen and (max-width: 620px) {
						.talon_wrap { grid-template-columns: 1fr; }
						.talon_img { aspect-ratio: 4 / 3; }
					}
				</style>
				<div class="talon">
					<div class="talon_title">Талон записи</div>
					<div class="talon_wrap">
						<div class="talon_img"><img src="' . get_doctor_photo( $doctor->ID ) . '"></div>
						<div class="talon_info">
							<div class="tal_info_row">Вы записаны<br> в МЦ ООО &quot;ПОЛИМЕДИКА&quot;</div>
							<div class="tal_info_row"><span>На дату:</span><br>' . $my_formatted_date . '</div>
							<div class="tal_info_row"><span>К доктору:</span><br>' . $position->name . '<br>' . $doc_fio . '</div>
							<div class="tal_info_row"><span>На прием/процедуру:</span><br>' . $serviceName . '</div>
						</div>
					</div>
					</div>';
			}
		} else {
			$message = '<div class="mssg">Не удалось выполнить запись.</div>';
		}
	}
}
get_header();
?>
<div class="container" style="min-height: 600px" >

	<div class="block-name center mt20">Запись</div>
	<div class="block-title center">Результат записи на прием</div>

		<?php if ( isset( $message ) ) : ?>
			<p><?php echo $message; ?></p>
		<?php else : ?>
			<p>Произошла ошибка. Попробуйте позже.</p>
		<?php endif; ?>
</div>
<?php 
	preg_match( '/\/[a-zA-Z0-9_-]+\/.*/', $url, $matches );
	echo '<script>console.log("' . $matches[0] . '");</script>';
?>
<?php
get_footer();


